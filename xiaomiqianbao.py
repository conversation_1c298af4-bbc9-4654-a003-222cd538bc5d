# !/usr/bin/python3
# -- coding: utf-8 --
# -------------------------------
# 小米钱包
# <AUTHOR>
# @Time 2025.06.08
# @Description
# ✨ 功能：
#       小米钱包 0 元领视频会员活动, 看广告任务可以获得会员时长，会员时长可以兑换视频会员：芒果、腾讯、爱奇艺、优酷等等
# ✨ 抓包步骤：
#       打开抓包软件，打开小米钱包，进入活动 “天天领视频会员”，找到带有 https://account.xiaomi.com/pass/serviceLogin 请求的 Cookie 值
# ✨ 变量示例：
#       export XMQB_COOKIES='fidNonce=xx==; passToken=xxx....'，多账号换行分割
# -------------------------------
# cron "33 33 9 * * *" script-path=xxx.py,tag=匹配cron用
# const $ = new Env('小米钱包');
# -------------------------------
from tools.common import BaseRun
from urllib.parse import urlparse
from collections import OrderedDict
import requests
import os
import hashlib
import base64
import json
import datetime
class XMQBCrypt:

    @classmethod
    def build_security_signature(
        cls,
        app_id,
        url,
        parameters,
        security_key
    ) -> str:
        """
        构建安全签名字符串

        Args:
            app_id: 应用标识符
            url: 请求URL地址
            parameters: 请求参数映射
            security_key: 安全密钥

        Returns:
            生成的安全签名字符串

        Raises:
            InvalidParameterException: 当安全密钥为空时抛出异常
        """
        # 验证安全密钥
        if not security_key:
            raise ("security is not nullable")


        # 初始化签名组件列表
        sign_components = []

        # 添加应用ID(如果存在，转换为大写)
        if app_id:
            sign_components.append(app_id.upper())

        # 添加URL的编码路径(如果存在)
        if url:
            parsed_url = urlparse(url)
            sign_components.append(parsed_url.path)

        # 处理参数(如果存在)
        if parameters:
            # 使用OrderedDict确保参数按键名排序
            sorted_params = OrderedDict(sorted(parameters.items()))
            # 将参数格式化为"key=value"形式
            sign_components.extend(f"{key}={value}" for key, value in sorted_params.items())

        # 添加安全密钥
        sign_components.append(security_key)

        # 使用'&'连接所有组件
        final_signature = "&".join(sign_components)

        # 返回加密后的签名(这里假设f是一个加密函数)
        return cls.encode_sha1_base64(final_signature)  # 注意：这里的f函数需要自己实现
    @classmethod
    def encode_sha1_base64(
        cls,
        content
    ):
        """
        将输入字符串进行 SHA1 哈希处理后转为 Base64 编码

        Args:
            content: 需要加密的字符串
            encoding_flags: base64编码选项

        Returns:
            base64编码后的字符串，如果输入为None则返回None

        Raises:
            EncryptionError: 加密过程发生错误时抛出
        """
        if content is None:
            return None

        try:
            # 计算SHA1哈希值
            sha1_bytes = cls.calculate_sha1(content.encode('utf-8'))
            if sha1_bytes is None:
                return None
            # 转换为base64编码
            return base64.b64encode(sha1_bytes).decode('utf-8')
        except Exception as e:
            raise (f"SHA1加密失败: {str(e)}")
    @classmethod
    def calculate_sha1(cls, data):
        """
        计算输入数据的SHA1哈希值

        Args:
            data: 需要计算哈希值的字节数据

        Returns:
            SHA1哈希值的字节串，如果输入为None或空则返回None

        Raises:
            EncryptionError: 计算SHA1过程发生错误时抛出
        """
        if not data:
            return None

        try:
            sha1_hash = hashlib.sha1()
            sha1_hash.update(data)
            return sha1_hash.digest()
        except Exception as e:
            raise f"SHA1计算失败: {str(e)}"
class Run(BaseRun):
    #@override
    def init_vars(self):
        self.session = requests.Session()
        self.session.verify = False
        self.headers = {
            'User-Agent': "23013RK75C/mondrian; HyperOS/OS2.0.3.0.VMNCNXM E/OS2.0 B/S L/zh-CN LO/CN APP/xiaomi.account APPV/********* MK/UmVkbWkgSzYw SDKV/6.2.6.master CPN/com.mipay.wallet",
            'Connection': "Keep-Alive",
            'Accept-Encoding': "gzip",
            'Content-Type': "application/x-www-form-urlencoded"
        }
        self.jrairstar_base_url = "https://m.jr.airstarfinance.net"
        self.jrairstar_api_base_url = "https://api.jr.airstarfinance.net"
        self.account_base_url = "https://account.xiaomi.com/"
    #@override
    def process_vars(self, info):
        self.cookie = info
        self.headers.update({'Cookie': info})
        self.session.headers.update(self.headers)

    #@override
    def process(self, info, _):
        self.process_vars(info)
        self.logger.info("===> 福利中心签到")
        session = self.mjrmicom_login()
        if session is not None:
            self.mjrmicom_sign(session)
        session = self.jrairstar_login()
        if session is not None:
            self.logger.info("===> 做任务")
            self.jrairstar_do_tasks(session)
            self.logger.info("===> 获取用户信息")
            self.get_personal_info(session)

    def get_login_cookies(self, app_name, sid):
        """
        将输入字符串进行 SHA1 哈希处理后转为 Base64 编码

        Args:
            app_name: 应用名称
            sid: 作用域

        Returns:
            客户端密钥，token 刷新 url
        """
        url = f"{self.account_base_url}/pass/serviceLogin"
        params = {
            '_json': "true",
            'appName': app_name,
            'sid': sid,
            '_locale': "zh_CN"
        }
        response = requests.get(url, params=params, headers=self.headers)
        res_text = response.text
        res_json = json.loads(res_text.removeprefix("&&&START&&&"))
        ssecurity = res_json.get("ssecurity")
        tree_map = {
            "nonce": f"{res_json.get('nonce')}",
        }
        client_sign = XMQBCrypt.build_security_signature(None, None, tree_map, ssecurity)
        refresh_url = res_json.get("location")
        params = {
            "_userIdNeedEncrypt": "true",
            "clientSign": client_sign
        }
        response = requests.get(refresh_url, params=params, headers=self.headers)
        login_cookies = requests.utils.dict_from_cookiejar(response.cookies)
        return login_cookies
    def get_session(self, cookie) -> requests.Session:
        cookie_str = '; '.join([f"{key}={value}" for key, value in cookie.items()])
        headers = self.headers.copy()
        headers.update({"Cookie": cookie_str})
        s = requests.Session()
        s.headers = headers
        s.cookies = requests.utils.cookiejar_from_dict(cookie)
        return s
    def jrairstar_login(self):
        login_cookies = self.get_login_cookies(app_name="com.mipay.wallet",
                                                               sid="jrairstar")
        if not login_cookies.get("serviceToken"):
            self.logger.error("❌ 检查登录状况失败，请更新 Cookie")
            return None
        else:
            self.logger.info("✅ 领会员页、福利中心、天星借钱、做任务等--登录成功")
            new_cookie = {
                "cUserId": login_cookies.get("cUserId"),
                "jrairstar_serviceToken": login_cookies.get("serviceToken"),
                "jrairstar_ph": login_cookies.get("jrairstar_ph"),
                "jrairstar_slh": login_cookies.get("jrairstar_slh")
            }
            return self.get_session(new_cookie)

    def mjrmicom_login(self):
        login_cookies = self.get_login_cookies(app_name="com.mipay.wallet",
                                                               sid="mjrmicom")
        if not login_cookies.get("serviceToken"):
            self.logger.error("❌ 检查登录状况失败，请更新 Cookie")
            return None
        else:
            self.logger.info("✅ 每日签到页--登录成功")
            new_cookie = {
                "cUserId": login_cookies.get("cUserId"),
                "mjrmicom_serviceToken": login_cookies.get("serviceToken"),
                "mjrmicom_ph": login_cookies.get("mjrmicom_ph"),
                "mjrmicom_slh": login_cookies.get("mjrmicom_slh")
            }
            return self.get_session(new_cookie)

    def mjrmicom_sign(self, s: requests.Session):
        url = "https://m.jr.mi.com/mp/api/dailyWelfare/sign"
        params = {
            'appType': "6"
        }
        response = s.get(url, params=params)
        if response.status_code != 200:
            self.logger.error("❌ 每日签到失败")
            return
        res_json = response.json()
        if res_json.get("code") == 1:
            self.logger.info("✅ 每日签到成功")
            self.sign_reward = res_json['result']['redPacket'] / 100
        elif res_json.get("code") == 0:
            self.logger.info("⚠️  已经签到了")

    def jrairstar_do_tasks(self, s: requests.Session):
        # self.logger.info("==> 看广告领QQ音乐会员时长")
        # self.jrairstar_do_qqmusic_task(s)
        self.logger.info("==> 做福利中心提现卡任务")
        self.jrairstar_do_welfarecenter_task(s)
        self.logger.info("==> 福利中心提现")
        self.jrairstar_do_withdraw(s)
        self.logger.info("==> 天星借钱-每周抽奖")
        self.jrairstar_do_weekly_lottery(s)
        self.logger.info("==> 看广告领视频会员时长")
        self.jrairstar_do_video_task(s)

    def jrairstar_do_video_task(self, s: requests.Session):
        """
        看广告领视频会员时长
        """
        for i in range(1, 5):
            #完成任务
            url = f"{self.jrairstar_base_url}/mp/api/generalActivity/completeTask"
            params = {
                'activityCode': "2211-videoWelfare",
                'app': "com.mipay.wallet",
                'channel': "mipay_indexcard_TVcard",
                'taskId': "813",
            }
            response = s.get(url, params=params)
            if response.status_code != 200:
                self.logger.error(f"❌ 请求失败，原因：{response.text}")
                return False
            data = response.json()
            if not data["success"]:
                self.logger.info("ℹ️  任务奖励已全部领取完成！！")
                return False
            url = f"{self.jrairstar_base_url}/mp/api/generalActivity/luckDraw"
            params = {
                'activityCode': "2211-videoWelfare",
                'userTaskId': data['value'],
            }
            response = s.get(url, params=params)
            if response.status_code != 200:
                self.logger.error(f"❌ 请求失败，原因：{response.text}")
                return False
            data = response.json()
            if data["success"]:
                self.logger.info(f"✅ 完成第 {i} 个任务成功，领到 {data['value']['prizeInfo']['prizeGiveDesc2']} {data['value']['prizeInfo']['prizeName']}")
            else:
                self.logger.error(f'❌ 领取奖励失败，原因：{data}')
    def jrairstar_do_qqmusic_task(self, s: requests.Session):
        """
        看广告领 QQ 音乐会员时长
        """
        i = 1
        while True:
            #完成任务阶段1
            url = f"{self.jrairstar_base_url}/mp/api/qqMusicActivity/finishMusicTask"
            payload = {
                'jrairstar_ph': s.cookies.get("jrairstar_ph"),
                'session_id': "40428b87-0147-42c5-8536-5682c6f5ea841737569677150"
            }
            response = s.post(url, data=payload)
            res_json = response.json()
            if response.status_code != 200:
                self.logger.error(f"❌ 请求失败，原因：{response.text}")
                return False
            elif not res_json.get('value'):
                self.logger.error(f"⚠️  请求异常，原因：{response.text}")
                return False
            #完成任务阶段2
            url = f"{self.jrairstar_base_url}/mp/api/generalActivity/completeTask"
            params = {
                'activityCode': "qq-music-201303",
                'taskId': "789",
                'app': "com.mipay.wallet",
                'channel': "mipay_FLbanner_mymusic",
            }
            response = s.get(url, params=params)
            if response.status_code != 200:
                self.logger.error(f"❌ 请求失败，原因：{response.text}")
                return False
            data = response.json()
            if not data["success"]:
                self.logger.info("ℹ️  任务奖励已全部领取完成！！")
                return False
            url = f"{self.jrairstar_base_url}/mp/api/generalActivity/luckDraw"
            params = {
                "device": "mondrian",
                'activityCode': "qq-music-201303",
                'userTaskId': data['value'],
                'app': "com.mipay.wallet",
            }
            response = s.get(url, params=params)
            if response.status_code != 200:
                self.logger.error(f"❌ 请求失败，原因：{response.text}")
                return False
            data = response.json()
            if data["success"]:
                self.logger.info(f"✅ 完成第 {i} 个任务成功，领到 {data['value']['prizeInfo']['prizeGiveDesc2']} {data['value']['prizeInfo']['prizeName']}")
            else:
                self.logger.error(f'❌ 领取奖励失败，原因：{data}')

    def jrairstar_do_welfarecenter_task(self, s: requests.Session):
        """
        做福利中心提现卡任务
        """
        # 1. 获取任务列表
        params = {
            'isMiui': "true",
            'channel': "814",
            "appType": "6"
        }
        url = f"{self.jrairstar_base_url}/mp/api/dailyWelfare/todayTaskList"
        response = s.get(url, params=params)
        if response.status_code != 200:
            self.logger.error(f"❌ 请求失败，原因：{response.text}")
            return False
        res_json = response.json()
        if not res_json["msg"] == "OK":
            self.logger.error(f"❌ 请求失败，原因：{res_json}")
            return False
        tasks = res_json['result']

        jrairstar_ph = s.cookies.get('jrairstar_ph')

        # 2. 唤起任务并完成任务
        for task in tasks:
            if task['completeTime'] == 0:
                task_code = task['taskCode']
                task_name = task['taskName']
                if not task_code.startswith("GENERAL_TASK_") and not task_code.startswith("SIGN_WALLET_FULI_"):
                    self.logger.info(f"⚠️  {task_name} 不是常规任务，跳过")
                    continue
                # 唤起任务
                payload = {
                    "taskCode": task_code,
                    "yimiData": "{\"clientInfo\":{\"deviceInfo\":{\"androidVersion\":\"35\",\"device\":\"mondrian\",\"miuiVersion\":816,\"miuiVersionName\":\"V816\",\"model\":\"23013RK75C\",\"restrictImei\":\"true\",\"screenHeight\":914,\"screenWidth\":411},\"userInfo\":{\"androidId\":\"42efef3b84faa85d\",\"connectionType\":\"WIFI\",\"oaid\":\"444cfc5462a58934\",\"country\":\"CN\",\"isPersonalizedAdEnabled\":true,\"language\":\"zh-rCN\"},\"appInfo\":{\"packageName\":\"com.mipay.wallet\",\"version\":\"6.91.1.5302.2333\"},\"context\":{},\"impRequests\":[{\"adsCount\":1,\"tagId\":\"*********\"}]}}",
                    "jrairstar_ph": jrairstar_ph
                }
                url = f"{self.jrairstar_base_url}/mp/api/dailyWelfare/drawTask"
                response = s.post(url, data=payload)
                if response.status_code != 200:
                    self.logger.error(f"❌ 唤起任务请求失败，原因：{response.text}")
                    continue
                res_json = response.json()
                if not response.json()["msg"] == "OK":
                    self.logger.error(f"❌ 唤起任务请求失败，原因：{res_json}")
                    continue

                # 完成任务
                url = f"{self.jrairstar_base_url}/mp/api/dailyWelfare/completeTask"
                payload = {
                    "app": "com.mipay.wallet",
                    "versionCode": "20577603",
                    "versionName": "6.91.1.5302.2333",
                    "isNfcPhone": "true",
                    "channel": "uma_mipay_home_icon",
                    "appType": "6",
                    "miui": "true",
                    "taskCode": task_code,
                    "jrairstar_ph": jrairstar_ph
                }
                response = s.post(url, data=payload)
                if response.status_code != 200:
                    self.logger.error(f"❌ 完成 {task_name} 任务请求失败，原因：{response.text}")
                    continue
                if not response.json()["msg"] == "OK":
                    self.logger.error(f"❌ 完成 {task_name} 任务请求失败，原因：{res_json}")
                    continue

        # 3. 查询可以领取的任务奖励并领取奖励
        params = {
            'isMiui': "true",
            'channel': "814",
            "appType": "6"
        }

        url = f"{self.jrairstar_base_url}/mp/api/dailyWelfare/todayTaskList"
        response = s.get(url, params=params)
        if response.status_code != 200:
            self.logger.error(f"❌ 查询可以领取的任务奖励请求失败，原因：{response.text}")
            return False
        res_json = response.json()
        if not response.json()["msg"] == "OK":
            self.logger.error(f"❌ 查询可以领取的任务奖励请求失败，原因：{res_json}")
            return False
        tasks = res_json['result']
        for task in tasks:
            if task['completeTime'] > 0 and task['buttonText'] != '已领取':
                url = f"{self.jrairstar_base_url}/mp/api/dailyWelfare/awardTask"
                task_code = task['taskCode']
                task_name = task['taskName']
                payload = {
                    "app": "com.mipay.wallet",
                    "versionCode": "20577603",
                    "versionName": "6.91.1.5302.2333",
                    "isNfcPhone": "true",
                    "channel": "uma_mipay_home_icon",
                    "appType": "6",
                    "miui": "true",
                    "taskCode": task_code,
                    "jrairstar_ph": jrairstar_ph
                }
                response = s.post(url, data=payload)
                if response.status_code != 200:
                    self.logger.error(f"❌ 领取任务 {task_name} 奖励请求失败，原因：{response.text}")
                    continue
                res_json = response.json()
                if not res_json["msg"] == "OK":
                    self.logger.error(f"❌ 领取任务 {task_name} 奖励请求失败，原因：{res_json}")
                    continue
                if res_json["result"]["chance"]:
                    self.logger.info(f"✅ 领取任务 {task_name} 奖励成功, 获得 {res_json['result']['chance'] / 100} 元提现卡")

    def jrairstar_withdraw_cards(self, s: requests.Session):
        """
        查看待提现的提现卡列表

        Returns:
            list: 待提现的提现卡ID列表，如果没有可提现的卡则返回空列表
        """
        url = f"{self.jrairstar_base_url}/mp/api/dailyWelfare/withdrawCardDetail"

        response = s.get(url)
        if response.status_code != 200:
            self.logger.error(f"❌ 查看待提现卡列表请求失败，原因：{response.text}")
            return []

        res_json = response.json()
        if res_json.get("code") != 1:
            self.logger.error(f"❌ 查看待提现卡列表失败，原因：{res_json}")
            return []

        result = res_json.get("result", {})
        count = result.get("count", 0)

        # 处理提现卡列表为空的情况：{"code":1,"msg":"OK","result":{"count":0}}
        if count == 0:
            self.logger.info("ℹ️  当前没有可提现的提现卡")
            return []

        cards = result.get("result", [])

        if not cards:
            self.logger.info("ℹ️  当前没有可提现的提现卡")
            return []

        # 筛选状态为1（可提现）的卡
        available_cards = [card for card in cards if card.get("status") == 1]

        if not available_cards:
            self.logger.info("ℹ️  当前没有可提现的提现卡")
            return []

        card_ids = [str(card["id"]) for card in available_cards]
        total_value = sum(card["value"] for card in available_cards)

        self.logger.info(f"✅ 发现 {len(available_cards)} 张可提现卡，总价值：{total_value} 分")
        for card in available_cards:
            self.logger.info(f"   - 卡ID: {card['id']}, 价值: {card['value']} 分, 类型: {card['type']}")

        return card_ids

    def jrairstar_do_withdraw(self, s: requests.Session):
        """
        执行福利中心提现操作
        """
        # 1. 查看待提现的提现卡列表
        card_ids = self.jrairstar_withdraw_cards(s)

        if not card_ids:
            return

        # 2. 执行提现
        url = f"{self.jrairstar_base_url}/mp/api/dailyWelfare/withdraw"

        jrairstar_ph = s.cookies.get('jrairstar_ph')
        if not jrairstar_ph:
            self.logger.error("❌ 无法获取jrairstar_ph，提现失败")
            return

        payload = {
            'card': ",".join(card_ids),
            'system': "1",
            'appType': "6",
            'jrairstar_ph': jrairstar_ph
        }

        response = s.post(url, data=payload)
        if response.status_code != 200:
            self.logger.error(f"❌ 提现请求失败，原因：{response.text}")
            return

        res_json = response.json()
        if res_json.get("code") == 1 and res_json.get("msg") == "OK":
            self.logger.info(f"✅ 提现成功！已提现 {len(card_ids)} 张提现卡")
        else:
            self.logger.error(f"❌ 提现失败，原因：{res_json}")

    def is_monday(self):
        """
        检查今天是否为周一

        Returns:
            bool: 如果今天是周一返回True，否则返回False
        """
        today = datetime.datetime.now()
        return today.weekday() == 0  # 0表示周一

    def jrairstar_get_weekly_lottery_tasks(self, s: requests.Session):
        """
        获取天星借钱周周抽奖任务列表，筛选包含"无门槛"的任务

        Returns:
            list: 包含无门槛任务的列表，每个任务包含taskId、activityCode、prizeId等信息
        """
        cUserId = s.cookies.get('cUserId')
        if not cUserId:
            self.logger.error("❌ 无法获取cUserId，获取抽奖任务失败")
            return []

        url = f"{self.jrairstar_api_base_url}/ca/userRightsWeek/homeDetail"
        params = {
            'code': '',
            'device': '4',
            'imei': '',
            'lon': '',
            'lat': '',
            'app': 'com.mipay.wallet',
            'cUserId': cUserId
        }

        response = s.post(url, params=params)
        if response.status_code != 200:
            self.logger.error(f"❌ 获取抽奖任务列表请求失败，原因：{response.text}")
            return []

        res_json = response.json()
        if not res_json.get("success"):
            self.logger.error(f"❌ 获取抽奖任务列表失败，原因：{res_json}")
            return []

        data = res_json.get("data", {})
        task_info = data.get("taskInfo", {})
        tasks = task_info.get("tasks", [])

        # 筛选包含"无门槛"的任务
        no_threshold_tasks = []
        for task in tasks:
            title = task.get("title", "")
            if "无门槛" in title and task.get("status") == 0:  # status=0表示未完成
                activity_code = data.get("activity", {}).get("code", "")
                task_data = {
                    "taskId": task.get("taskId"),
                    "activityCode": activity_code,
                    "prizeId": task.get("prizeDTO", {}).get("prizeId"),
                    "prizeName": task.get("prizeDTO", {}).get("name"),
                    "title": title,
                    "subTitle": task.get("subTitle")
                }
                no_threshold_tasks.append(task_data)
                self.logger.info(f"✅ 发现无门槛任务：{title} - {task.get('subTitle')}")

        if not no_threshold_tasks:
            self.logger.info("ℹ️  当前没有可参与的无门槛抽奖任务")

        return no_threshold_tasks

    def jrairstar_fetch_lottery_chance(self, s: requests.Session, task_data):
        """
        获取抽奖机会

        Args:
            s: requests.Session对象
            task_data: 任务数据，包含taskId和activityCode

        Returns:
            int: 抽奖机会数量，0表示没有机会
        """
        cUserId = s.cookies.get('cUserId')
        if not cUserId:
            self.logger.error("❌ 无法获取cUserId，获取抽奖机会失败")
            return 0

        url = f"{self.jrairstar_api_base_url}/ca/luckyDraw/fetchChance"
        payload = {
            'taskId': str(task_data["taskId"]),
            'activityCode': task_data["activityCode"],
            'app': 'com.mipay.wallet',
            'cUserId': cUserId
        }

        response = s.post(url, data=payload)
        if response.status_code != 200:
            self.logger.error(f"❌ 获取抽奖机会请求失败，原因：{response.text}")
            return 0

        res_json = response.json()
        if not res_json.get("success"):
            self.logger.error(f"❌ 获取抽奖机会失败，原因：{res_json}")
            return 0

        data = res_json.get("data", {})
        chance_count = data.get("chanceCount", 0)

        if chance_count > 0:
            self.logger.info(f"✅ 获取到 {chance_count} 次抽奖机会")
        else:
            self.logger.info("ℹ️  当前没有抽奖机会")

        return chance_count

    def jrairstar_apply_lottery(self, s: requests.Session, task_data):
        """
        执行抽奖报名

        Args:
            s: requests.Session对象
            task_data: 任务数据，包含taskId、prizeId等

        Returns:
            bool: 抽奖报名是否成功
        """
        cUserId = s.cookies.get('cUserId')
        if not cUserId:
            self.logger.error("❌ 无法获取cUserId，抽奖报名失败")
            return False

        url = f"{self.jrairstar_api_base_url}/ca/userRightsWeek/applyLuckyDraw"
        params = {
            'type': '9',
            'prizeId': str(task_data["prizeId"]),
            'taskId': str(task_data["taskId"]),
            'app': 'com.mipay.wallet',
            'cUserId': cUserId
        }

        response = s.post(url, params=params)
        if response.status_code != 200:
            self.logger.error(f"❌ 抽奖报名请求失败，原因：{response.text}")
            return False

        res_json = response.json()
        if res_json.get("success") and res_json.get("name") == "Success":
            self.logger.info(f"✅ 抽奖报名成功！奖品：{task_data['prizeName']}")
            return True
        else:
            self.logger.error(f"❌ 抽奖报名失败，原因：{res_json}")
            return False

    def jrairstar_do_weekly_lottery(self, s: requests.Session):
        """
        执行天星借钱每周抽奖任务（仅在周一执行）
        """
        # 检查是否为周一
        if not self.is_monday():
            self.logger.info("ℹ️  今天不是周一，跳过天星借钱每周抽奖")
            return

        self.logger.info("✅ 今天是周一，开始执行天星借钱每周抽奖")

        # 1. 获取无门槛抽奖任务列表
        tasks = self.jrairstar_get_weekly_lottery_tasks(s)

        if not tasks:
            return

        # 2. 对每个无门槛任务进行抽奖
        success_count = 0
        for task in tasks:
            self.logger.info(f"==> 处理任务：{task['title']}")

            # 获取抽奖机会
            chance_count = self.jrairstar_fetch_lottery_chance(s, task)

            if chance_count > 0:
                # 执行抽奖报名
                if self.jrairstar_apply_lottery(s, task):
                    success_count += 1
                else:
                    self.logger.error(f"❌ 任务 {task['title']} 抽奖报名失败")
            else:
                self.logger.info(f"ℹ️  任务 {task['title']} 没有抽奖机会，跳过")

        if success_count > 0:
            self.logger.info(f"✅ 天星借钱每周抽奖完成，成功报名 {success_count} 个抽奖")
        else:
            self.logger.info("ℹ️  天星借钱每周抽奖完成，但没有成功报名任何抽奖")
    # 获取个人信息
    def get_personal_info(self, s: requests.Session):
        url = f"{self.jrairstar_base_url}/mp/api/generalActivity/queryUserGoldRichSum"
        params = {
            'app': "com.mipay.wallet",
            'activityCode': "2211-videoWelfare"
        }
        response = s.get(url, params=params)
        data = response.json()
        if response.status_code != 200:
            self.logger.error(f"❌ 请求失败，原因：{response.text}")
            return False
        if data and data["success"]:
            self.logger.info(f"✅ 当前视频会员时长：{data['value'] / 100:.2f} 天")
        else:
            self.logger.error(f"❌ 查询当前视频会员时长失败，原因：{data}")

        params = {
            'app': "com.mipay.wallet",
            'activityCode': "qq-music-201303"
        }
        response = s.get(url, params=params)
        data = response.json()
        if response.status_code != 200:
            self.logger.error(f"❌ 请求失败，原因：{response.text}")
            return False
        if data and data["success"]:
            self.logger.info(f"✅ 当前音乐会员时长：{data['value'] / 60} 小时")
        else:
            self.logger.error(f"❌ 查询当前音乐会员时长失败，原因：{data}")


if __name__ == "__main__":
    app_name = "小米钱包"
    app_env_name = "XMQB_COOKIES"
    local_script_name = os.path.basename(__file__)
    local_version = '2025.06.13'
    run = Run(app_name=app_name,
              app_env_name=app_env_name,
              script_version=local_version)
    run.main()



