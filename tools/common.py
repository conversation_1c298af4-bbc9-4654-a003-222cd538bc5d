from datetime import datetime
from enum import Enum
from typing import Optional
from typing import Any
from typing import List
from urllib3.exceptions import InsecureRequestWarning
from abc import ABC, abstractmethod
from http.cookies import SimpleCookie
import random
import time
import os
import urllib3
import traceback
urllib3.disable_warnings(InsecureRequestWarning)
class ScriptStatus(Enum):
    SUCCESS = "正常"
    WARNING = "正常"
    FAILURE = "异常"
class ScriptLogger:
    '''日志记录器，用于记录日志并获取通知消息'''
    def __init__(self):
        self.notify_message = ""
        self.status = ScriptStatus.SUCCESS
    def print(self, one_message, append_notify, end):
        '''输出运行日志'''
        if one_message:
            print(f"{one_message}",end=end)
            if append_notify:
                self.notify_message += f"{one_message}\n"
    def info(self, one_message, append_notify=True, end='\n'):
        '''
        one_message: 消息
        append_notify: 是否添加到通知消息
        end: 结尾字符
        '''
        self.print(one_message, append_notify, end)
    def warn(self, one_message, append_notify=True, end='\n'):
        '''
        one_message: 消息
        append_notify: 是否添加到通知消息
        end: 结尾字符
        '''
        self.status = ScriptStatus.WARNING
        self.print(one_message, append_notify, end)
    def error(self, one_message, append_notify=True, end='\n'):
        '''
        one_message: 消息
        append_notify: 是否添加到通知消息
        end: 结尾字符
        '''
        self.status = ScriptStatus.FAILURE
        self.print(one_message, append_notify, end)
    def final(self, one_message):
        '''
        脚本结束时调用，将脚本运行结果放到通知开头
        one_message: 消息
        '''
        self.notify_message = f"{one_message}\n\n{self.notify_message}"
    def debug(self, one_message, end='\n'):
        #以 yyyy-mm-dd HH:mm:ss:ms 格式输出当前时间
        self.print(f"\033[0;31;40m{self.time()}: [DEBUG] {one_message}\033[0m", False, end)
    def get_notify_message(self):
        return self.notify_message
    def get_status(self):
        return self.status
    def time(self):
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
class BaseRun(ABC):
    def __init__(self, app_name: Optional[str] = None, app_env_name: Optional[str] = None, app_env_infos: Optional[List[Any]] = None, script_version: Optional[str] = None):
        self.logger = ScriptLogger()
        self.tools = ScriptTool()
        self.app_name = app_name
        self.app_env_name = app_env_name
        self.script_version = script_version
        self.app_env_infos = app_env_infos
        self.notify_switch = False
    @abstractmethod
    def init_vars(self):
        """脚本用到的变量和数据初始化，必须由子类实现"""
        pass

    @abstractmethod
    def process(self, info, index = 1):
        """脚本主逻辑，必须由子类实现，每个账号都会执行一次
        info: 账号信息
        index: 账号索引
        """
        pass
    @abstractmethod
    def process_vars(self, info):
        """脚本处理每个环境变量的账号信息，必须由子类实现，每个账号都会执行一次
        info: 账号信息
        """
        pass
    def notify(self):
        """发送通知，这是一个钩子方法，可以选择重写"""
        send = self.tools.load_send()
        if send:
            send(f'{self.app_name}挂机通知【{self.logger.status.value}】', self.logger.notify_message)
    def init_envinfo(self):
        '''
        脚本运行的环境变量初始化
        '''
        if self.app_env_infos:
            return
        # 多变量情况
        """
        对于存在多个环境变量的情况，需要声明 app_env_name 为多个环境变量名，用空格分隔
        将每个环境变量值按行分割，每个环境变量值的行数需要一致
        在每个环境变量值的行数一致的情况下，每个环境变量值的第 i 行与其他环境变量值的第 i 行一一对应，作为一个用户信息
        {
            "env_name1": ["env_value1_line1", "env_value1_line2", "env_value1_line3"],
            "env_name2": ["env_value2_line1", "env_value2_line2", "env_value2_line3"],
        }
        """
        if len(self.app_env_name.split()) > 1:
            app_env_names = self.app_env_name.split()
            app_env_infos = []
            env_values = {}
            # Get all environment variable values
            for env_name in app_env_names:
                env_value = os.getenv(env_name)
                if not env_value:
                    self.logger.error(f"未填写 {env_name} 变量\n青龙可在环境变量设置 {env_name}")
                    exit()
                env_values[env_name] = self.tools.env_split(env_value)
            # Validate all env vars have same number of values
            first_len = len(env_values[app_env_names[0]])
            if not all(len(values) == first_len for values in env_values.values()):
                self.logger.error("所有环境变量的值的行数必须一致")
                exit(0)
            # Create combined user info list
            for i in range(first_len):
                env_info = {
                    name: env_values[name][i]
                    for name in app_env_names
                }
                app_env_infos.append(env_info)
            self.app_env_infos = app_env_infos
        else:
            app_env_str = os.environ.get(self.app_env_name)
            if not app_env_str:
                self.logger.error(f"未填写 {self.app_env_name} 变量, 青龙可在环境变量设置 {self.app_env_name}")
            else:
                app_envs = self.tools.env_split(app_env_str)
                if len(app_envs) > 0:
                    self.app_env_infos = app_envs
    # 模板方法，定义脚本运行的框架
    def main(self):
        '''
        脚本运行的主方法
        '''
        if not self.tools.dev():
            self.tools.random_delay_run()
            self.notify_switch = True
        try:
            self.logger.info(f"--- 脚本运行开始，当前版本：{self.script_version} ---")
            #初始化
            self.init_envinfo()
            self.init_vars()
            if self.app_env_infos:
                self.logger.info(f"=======\t共获取到 {len(self.app_env_infos)} 个账号")
                #主流程，多账号
                for index, info in enumerate(self.app_env_infos):
                    try:
                        self.logger.info(f"=======\t第 {index + 1} 个账号开始执行")
                        #单个账号执行
                        self.process(info, index + 1)
                    except Exception:
                        crash_info = self.tools.get_detailed_crash_info()
                        if crash_info:
                            f, func, line = crash_info['main_location']
                            exc_type = crash_info['exception_type']
                            exc_msg = crash_info['exception_message']

                            # 显示主程序错误位置和错误原因
                            self.logger.error(f"运行出错: {exc_type}: {exc_msg}")
                            if f and func and line:
                                self.logger.error(f"错误位置: {os.path.basename(f)} -> {func}() 第{line}行")

                            # 显示主程序相关的调用栈
                            if crash_info['main_traceback']:
                                self.logger.error(f"主程序调用栈:\n{crash_info['main_traceback']}")

                            # 开发环境下显示完整调用栈
                            if self.tools.dev():
                                self.logger.debug(f"完整调用栈:\n{crash_info['full_traceback']}")
                        else:
                            self.logger.error("运行出错，但无法获取详细错误信息")
                            self.logger.debug(traceback.format_exc())
                    finally:
                        self.logger.info(f"=======\t第 {index + 1} 个账号执行完毕")
            else:
                self.logger.error("账号信息获取失败，请检查变量")

        except Exception:
            crash_info = self.tools.get_detailed_crash_info()
            if crash_info:
                f, func, line = crash_info['main_location']
                exc_type = crash_info['exception_type']
                exc_msg = crash_info['exception_message']

                # 显示主程序错误位置和错误原因
                self.logger.error(f"脚本运行出错: {exc_type}: {exc_msg}")
                if f and func and line:
                    self.logger.error(f"错误位置: {os.path.basename(f)} -> {func}() 第{line}行")

                # 显示主程序相关的调用栈
                if crash_info['main_traceback']:
                    self.logger.error(f"主程序调用栈:\n{crash_info['main_traceback']}")

                # 开发环境下显示完整调用栈
                if self.tools.dev():
                    self.logger.debug(f"完整调用栈:\n{crash_info['full_traceback']}")
            else:
                self.logger.error("脚本运行出错，但无法获取详细错误信息")
                self.logger.debug(traceback.format_exc())
        finally:
            self.logger.info(f"--- 脚本运行结束，当前版本：{self.script_version} ---")
            if self.notify_switch:
                self.notify()
            if self.tools.dev():
                self.logger.debug("通知预览：\n" + self.logger.get_notify_message())
    # 模板方法，定义脚本运行的框架
    async def async_main(self):
        '''
        脚本运行的主方法
        '''
        if not self.tools.dev():
            self.tools.random_delay_run()
            self.notify_switch = True
        try:
            self.logger.info(f"--- 脚本运行开始，当前版本：{self.script_version} ---")
            #初始化
            self.init_envinfo()
            self.init_vars()
            if self.app_env_infos:
                self.logger.info(f"=======\t共获取到 {len(self.app_env_infos)} 个账号")
                #主流程，多账号
                for index, info in enumerate(self.app_env_infos):
                    try:
                        self.logger.info(f"=======\t第 {index + 1} 个账号开始执行")
                        #单个账号执行
                        await self.process(info, index + 1)
                    except Exception:
                        crash_info = self.tools.get_detailed_crash_info()
                        if crash_info:
                            f, func, line = crash_info['main_location']
                            exc_type = crash_info['exception_type']
                            exc_msg = crash_info['exception_message']

                            # 显示主程序错误位置和错误原因
                            self.logger.error(f"运行出错: {exc_type}: {exc_msg}")
                            if f and func and line:
                                self.logger.error(f"错误位置: {os.path.basename(f)} -> {func}() 第{line}行")

                            # 显示主程序相关的调用栈
                            if crash_info['main_traceback']:
                                self.logger.error(f"主程序调用栈:\n{crash_info['main_traceback']}")

                            # 开发环境下显示完整调用栈
                            if self.tools.dev():
                                self.logger.debug(f"完整调用栈:\n{crash_info['full_traceback']}")
                        else:
                            self.logger.error("运行出错，但无法获取详细错误信息")
                            self.logger.debug(traceback.format_exc())
                    finally:
                        self.logger.info(f"=======\t第 {index + 1} 个账号执行完毕")
            else:
                self.logger.error("账号信息获取失败，请检查变量")

        except Exception:
            crash_info = self.tools.get_detailed_crash_info()
            if crash_info:
                f, func, line = crash_info['main_location']
                exc_type = crash_info['exception_type']
                exc_msg = crash_info['exception_message']

                # 显示主程序错误位置和错误原因
                self.logger.error(f"脚本运行出错: {exc_type}: {exc_msg}")
                if f and func and line:
                    self.logger.error(f"错误位置: {os.path.basename(f)} -> {func}() 第{line}行")

                # 显示主程序相关的调用栈
                if crash_info['main_traceback']:
                    self.logger.error(f"主程序调用栈:\n{crash_info['main_traceback']}")

                # 开发环境下显示完整调用栈
                if self.tools.dev():
                    self.logger.debug(f"完整调用栈:\n{crash_info['full_traceback']}")
            else:
                self.logger.error("脚本运行出错，但无法获取详细错误信息")
                self.logger.debug(traceback.format_exc())
        finally:
            self.logger.info(f"--- 脚本运行结束，当前版本：{self.script_version} ---")
            if self.notify_switch:
                self.notify()
            if self.tools.dev():
                self.logger.debug("通知预览：\n" + self.logger.get_notify_message())

class ScriptTool:
    work_path = os.getcwd()
    @staticmethod
    def env_split(env_str):
        '''
        将环境变量字符串分割为列表（处理多账号信息）
        '''
        if '\n' in env_str:
            hash_parts = env_str.split('\n')
            return (hash_parts)
        else:
            out_str = str(env_str)
            return ([out_str])
    @staticmethod
    def random_delay_run(min_delay=60, max_delay=120):
        '''
        随机延迟
        '''
        delay = random.uniform(min_delay, max_delay)
        print(f"\n随机延迟{delay}秒\n")
        time.sleep(delay)
    @staticmethod
    def load_send():
        '''
        加载通知服务
        '''
        notify_file = os.path.join(ScriptTool.work_path, "tools", "notify.py")
        if os.path.exists(notify_file):
            try:
                from tools.notify import send  # 导入模块的send为notify_send
                print("加载通知服务成功！")
                return send  # 返回导入的函数
            except ImportError:
                print("加载通知服务失败~")
        else:
            print("加载通知服务失败~")
        return False
    @staticmethod
    def dev():
        '''
        检测是否为开发环境
        '''
        env_file = os.path.join(ScriptTool.work_path,"config", ".env")
        if os.path.isfile(env_file):
            from dotenv import load_dotenv
            load_dotenv(env_file)
            return True
        else:
            return False
    @staticmethod
    def get_cookie_from_str(cookie_str):
        '''
        从字符串中获取 Cookie
        '''
        # 使用 SimpleCookie 解析 Cookie 字符串
        cookie = SimpleCookie()
        cookie.load(cookie_str)
        # 将解析的 Cookie 转换为字典
        return {key: morsel.value for key, morsel in cookie.items()}
    @staticmethod
    def get_timestamp(digits: int = 10) -> int:
        """
        获取指定位数的时间戳
        Args:
            digits: 时间戳位数 (10 或 13)
        Returns:
            int: 指定位数的时间戳
        """
        current = time.time()
        if digits == 10:
            return int(current)
        elif digits == 13:
            return int(current * 1000)
        else:
            raise ValueError("时间戳位数只支持10位或13位")
    @staticmethod
    def get_crash_location_in_main():
        """
        获取主程序文件中引发异常的最后一帧位置
        Returns:
            tuple: (文件名, 函数名, 行号)
        """
        import sys, os
        _, _, tb = sys.exc_info()
        last_match = None
        main_file = os.path.abspath(sys.argv[0])

        while tb:
            frame = tb.tb_frame
            filename = frame.f_globals.get('__file__', '')
            if os.path.abspath(filename) == main_file:
                func_name = frame.f_code.co_name
                line = tb.tb_lineno
                last_match = (filename, func_name, line)
            tb = tb.tb_next

        return last_match if last_match else (None, None, None)

    @staticmethod
    def get_detailed_crash_info():
        """
        获取详细的异常信息，包括主程序位置和错误原因
        Returns:
            dict: {
                'main_location': (文件名, 函数名, 行号),
                'exception_type': 异常类型,
                'exception_message': 异常消息,
                'full_traceback': 完整调用栈,
                'main_traceback': 主程序相关的调用栈
            }
        """
        import sys, os
        exc_type, exc_value, tb = sys.exc_info()

        if not tb:
            return None

        main_file = os.path.abspath(sys.argv[0])
        last_match = None
        main_frames = []

        # 遍历调用栈，收集主程序相关信息
        current_tb = tb
        while current_tb:
            frame = current_tb.tb_frame
            filename = frame.f_globals.get('__file__', '')

            if os.path.abspath(filename) == main_file:
                func_name = frame.f_code.co_name
                line = current_tb.tb_lineno
                last_match = (filename, func_name, line)

                # 收集主程序帧信息
                main_frames.append({
                    'filename': filename,
                    'function': func_name,
                    'line': line,
                    'code': frame.f_code
                })

            current_tb = current_tb.tb_next

        # 构建主程序相关的简化调用栈
        main_traceback_lines = []
        for frame_info in main_frames:
            main_traceback_lines.append(
                f"  File \"{frame_info['filename']}\", line {frame_info['line']}, in {frame_info['function']}"
            )

        return {
            'main_location': last_match if last_match else (None, None, None),
            'exception_type': exc_type.__name__ if exc_type else None,
            'exception_message': str(exc_value) if exc_value else None,
            'full_traceback': traceback.format_exc(),
            'main_traceback': '\n'.join(main_traceback_lines) if main_traceback_lines else None
        }

