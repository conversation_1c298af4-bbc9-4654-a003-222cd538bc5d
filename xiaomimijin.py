# !/usr/bin/python3
# -- coding: utf-8 --
# -------------------------------
# 小米米金
# <AUTHOR>
# @Time 2025.06.08
# @Description
# ✨ 功能：
#       小米米金任务自动化，完成各种任务获取米金奖励
# ✨ 抓包步骤：
#       打开抓包软件，打开小米钱包，进入米金活动页面，找到带有 https://account.xiaomi.com/pass/serviceLogin 请求的 Cookie 值
# ✨ 变量示例：
#       export XMMJ_COOKIES='fidNonce=xx==; passToken=xxx....'，多账号换行分割
# -------------------------------
# cron "33 44 9 * * *" script-path=xxx.py,tag=匹配cron用
# const $ = new Env('小米米金');
# -------------------------------
from tools.common import BaseRun
import requests
import json
import os


class Run(BaseRun):
    # @override
    def init_vars(self):
        self.session = requests.Session()
        self.session.verify = False
        self.headers = {
            'User-Agent': "Mozilla/5.0 (Linux; U; Android 15; zh-CN; 23013RK75C Build/AQ3A.240912.001; AppBundle/com.mipay.wallet; AppVersionName/6.91.1.5302.2333; AppVersionCode/********; MiuiVersion/stable-OS2.0.9.0.VMNCNXM; DeviceId/mondrian; NetworkType/WIFI; mix_version; WebViewVersion/136.0.7103.61) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.36 XiaoMi/MiuiBrowser/4.3",
            'Accept-Encoding': "gzip, deflate, br, zstd",
            'Content-Type': "application/json",
            'sec-ch-ua-platform': "\"Android\"",
            'x-user-agent': "channel/mishop platform/mishop.m",
            'equipmenttype': "4",
            'sec-ch-ua': "\"Chromium\";v=\"136\", \"Android WebView\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
            'sec-ch-ua-mobile': "?1",
            'origin': "https://m.mi.com",
            'x-requested-with': "com.mipay.wallet",
            'sec-fetch-site': "same-origin",
            'sec-fetch-mode': "cors",
            'sec-fetch-dest': "empty",
            'accept-language': "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            'priority': "u=1, i"
        }
        self.base_url = "https://m.mi.com"
        self.account_base_url = "https://account.xiaomi.com/"

    # @override
    def process_vars(self, info):
        self.cookie = info
        self.headers.update({'Cookie': info})
        self.session.headers.update(self.headers)

    # @override
    def process(self, info, _):
        self.process_vars(info)
        self.logger.info("===> 登录")
        self.generate_login_session(app_name="com.mipay.wallet", sid="mi_eshopm_go")
        self.logger.info("===> 获取任务列表")
        tasks = self.get_task_list()
        if tasks:
            self.logger.info(f"✅ 获取到 {len(tasks)} 个任务")
            self.logger.info("===> 开始执行任务")
            self.execute_tasks(tasks)
        else:
            self.logger.error("❌ 获取任务列表失败")

    def generate_login_session(self, app_name, sid):
        """
        生成登录的 session

        Args:
            app_name: 应用名称
            sid: 作用域
        """
        url = f"{self.account_base_url}/pass/serviceLogin"
        params = {
            '_json': "true",
            'appName': app_name,
            'sid': sid,
            '_locale': "zh_CN"
        }
        response = requests.get(url, params=params, headers=self.headers)
        res_text = response.text
        res_json = json.loads(res_text.removeprefix("&&&START&&&"))
        refresh_url = res_json.get("location")
        response = requests.get(refresh_url, params=params, headers=self.headers)
        login_cookies = requests.utils.dict_from_cookiejar(response.cookies)
        if not login_cookies.get("serviceToken"):
            self.logger.error("❌ 检查登录状况失败，请更新 Cookie")
            return None
        else:
            self.logger.info("✅ 登录成功")
            cookie_str = '; '.join([f"{key}={value}" for key, value in login_cookies.items()])
            headers = self.headers.copy()
            headers.update({"Cookie": cookie_str})
            s = requests.Session()
            s.headers = headers
            s.cookies = response.cookies
            self.session = s

    def get_task_list(self):
        """获取任务列表"""
        url = f"{self.base_url}/mtop/navi/venue/batch"
        params = {
            "page_id": "13880",
            "pdl": "mishop",
            "sign": "1a04e866dd9d421e5e16e21bb984b947"
        }

        payload = {
            "query_list": [
                {
                    "resolver": "infinite-task",
                    "sign": "ff8960139490adb9071ed47a34f179ff",
                    "parameter": "{\"actId\":\"6706c0695404a23dfb5b2cab\",\"taskTypeList\":[101,200,110,201,202]}",
                    "variable": {}
                }
            ]
        }

        try:
            response = self.session.post(url, params=params, data=json.dumps(payload))
            if response.status_code != 200:
                self.logger.error(f"❌ 请求失败，状态码：{response.status_code}")
                return None

            res_json = response.json()
            if 'data' in res_json and 'result_list' in res_json['data']:
                tasks = res_json['data']['result_list'][0]['components']
                return tasks
            else:
                self.logger.error(f"❌ 响应格式异常：{res_json}")
                return None

        except Exception as e:
            self.logger.error(f"❌ 获取任务列表异常：{str(e)}")
            return None

    def execute_tasks(self, tasks):
        """执行任务列表"""
        for task in tasks:
            try:
                self.do_task(task)
            except Exception as e:
                self.logger.error(f"❌ 执行任务异常：{str(e)}")

    def do_task(self, task):
        """执行单个任务"""
        task_name = task.get('taskName', '未知任务')
        self.logger.info(f"==> 处理任务：{task_name}")

        # 检查任务状态，status=2表示可以执行
        if task.get('status') != 2:
            self.logger.info(f"⚠️  任务 {task_name} 状态不可执行，跳过")
            return

        # 完成任务
        task_token = self.complete_task(task)
        if not task_token:
            self.logger.error(f"❌ 完成任务 {task_name} 失败")
            return

        # 领取奖励
        self.claim_reward(task, task_token)

    def complete_task(self, task):
        """完成任务"""
        url = f"{self.base_url}/mtop/mf/act/infinite/do"
        payload = [
            {},
            {
                "taskId": task['taskId'],
                "actId": task['actId']
            }
        ]
        try:
            response = self.session.post(url, data=json.dumps(payload))
            if response.status_code != 200:
                self.logger.error(f"❌ 完成任务请求失败，状态码：{response.status_code}")
                return None

            res_json = response.json()
            if 'data' in res_json and 'taskToken' in res_json['data']:
                return res_json['data']['taskToken']
            else:
                self.logger.error(f"❌ 完成任务响应异常：{res_json}")
                return None

        except Exception as e:
            self.logger.error(f"❌ 完成任务异常：{str(e)}")
            return None

    def claim_reward(self, task, task_token):
        """领取任务奖励"""
        task_type = task.get('taskType')
        task_name = task.get('taskName', '未知任务')

        # 根据任务类型选择不同的领取接口
        if task_type == 110:
            url = f"{self.base_url}/mtop/mf/act/infinite/done"
        elif task_type == 200:
            url = f"{self.base_url}/v2/mtop/act/lego/task/done/v2"
        else:
            self.logger.info(f"⚠️  任务类型 {task_type} 暂不支持领取奖励")
            return

        payload = [
            {},
            {
                "taskToken": task_token,
                "actId": task['actId'],
                "taskType": task_type
            }
        ]

        try:
            response = self.session.post(url, data=json.dumps(payload))
            if response.status_code != 200:
                self.logger.error(f"❌ 领取奖励请求失败，状态码：{response.status_code}")
                return

            res_json = response.json()
            if res_json.get('success', False) or res_json.get('code') == 0:
                self.logger.info(f"✅ 任务 {task_name} 奖励领取成功")
            else:
                self.logger.error(f"❌ 任务 {task_name} 奖励领取失败：{res_json}")

        except Exception as e:
            self.logger.error(f"❌ 领取奖励异常：{str(e)}")


if __name__ == "__main__":
    app_name = "小米米金"
    app_env_name = "XMMJ_COOKIES"
    local_script_name = os.path.basename(__file__)
    local_version = '2025.06.08'
    run = Run(app_name=app_name,
              app_env_name=app_env_name,
              script_version=local_version)
    run.main()
