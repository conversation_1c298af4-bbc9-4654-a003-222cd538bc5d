#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
携程旅行直接调用测试
使用真实API进行功能测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import json
import time
from xiecheng import Run


def test_real_api_calls():
    """测试真实API调用"""
    print("🌐 开始真实API调用测试...")

    # 检查环境变量
    auth_token = os.getenv('XIECHENG_TOKENS')
    if not auth_token:
        print("❌ 未设置 XIECHENG_TOKENS 环境变量")
        print("💡 请设置: export XIECHENG_TOKENS='your_auth_token'")
        return False

    try:
        # 创建实例
        run = Run(
            app_name="携程旅行直接测试",
            app_env_name="XIECHENG_TOKENS",
            script_version="test"
        )

        # 初始化
        run.init_vars()
        run.process_vars(auth_token)
        print(f"✅ 初始化成功，Auth Token: {auth_token[:20]}...")

        print("=" * 60)

        # 1. 测试签到API
        print("📅 1. 测试签到API...")
        try:
            run.sign_in()
            print("✅ 签到API调用成功")
            print(f"   连续签到: {run.continue_day} 天")
            print(f"   基础积分: {run.base_point}")
            print(f"   额外积分: {run.extra_point}")
            print(f"   携程积分: {run.extra_ctrip_point}")
        except Exception as e:
            print(f"❌ 签到API调用失败: {str(e)}")

        print("=" * 60)

        # 2. 测试获取任务列表API
        print("📋 2. 测试获取任务列表API...")
        try:
            tasks = run.get_task_list()
            print(f"✅ 任务列表API调用成功，获取到 {len(tasks)} 个任务")

            if tasks:
                # 分析任务
                browse_tasks = [t for t in tasks if t.get('eventType') == 'NO_REPEAT_BROWSE']
                pending_tasks = [t for t in tasks if t.get('status') == 0]
                completed_tasks = [t for t in tasks if t.get('status') == 3]

                print(f"   浏览任务: {len(browse_tasks)} 个")
                print(f"   待执行任务: {len(pending_tasks)} 个")
                print(f"   可领奖任务: {len(completed_tasks)} 个")

                # 显示前3个任务详情
                print("\n📝 任务详情（前3个）:")
                for i, task in enumerate(tasks[:3], 1):
                    status_map = {0: "待执行", 1: "进行中", 2: "已完成", 3: "可领奖"}
                    status = status_map.get(task.get('status'), "未知")
                    print(f"   {i}. {task.get('displayName', '未知任务')} - {status}")
                    print(f"      ID: {task.get('id')}")
                    print(f"      类型: {task.get('eventType', 'N/A')}")
                    if task.get('browseSeconds'):
                        print(f"      浏览时长: {task.get('browseSeconds')} 秒")
                    print()

        except Exception as e:
            print(f"❌ 任务列表API调用失败: {str(e)}")

        print("=" * 60)

        # 3. 测试任务执行API（如果有待执行的浏览任务）
        print("🎯 3. 测试任务执行API...")
        try:
            tasks = run.get_task_list()
            browse_tasks = [t for t in tasks if t.get('eventType') == 'NO_REPEAT_BROWSE' and t.get('status') == 0]

            if browse_tasks:
                test_task = browse_tasks[0]
                print(f"🚀 测试执行任务: {test_task.get('displayName')}")
                print(f"   任务ID: {test_task.get('id')}")
                print(f"   预计等待: {test_task.get('browseSeconds', 15)} 秒")

                # 实际执行任务
                run.execute_browse_task(test_task)
                print("✅ 任务执行API测试完成")

            else:
                print("ℹ️  没有可执行的浏览任务，跳过执行测试")

        except Exception as e:
            print(f"❌ 任务执行API测试失败: {str(e)}")

        print("=" * 60)

        # 4. 测试奖励领取API（如果有可领奖任务）
        print("🎁 4. 测试奖励领取API...")
        try:
            tasks = run.get_task_list()
            completed_task_ids = [t['id'] for t in tasks if t.get('status') == 3]

            if completed_task_ids:
                print(f"🎯 测试领取 {len(completed_task_ids)} 个任务的奖励")
                run.claim_task_rewards(completed_task_ids)
                print("✅ 奖励领取API测试完成")
                if run.total_task_points > 0:
                    print(f"   获得积分: {run.total_task_points}")
            else:
                print("ℹ️  没有可领奖任务，跳过奖励测试")

        except Exception as e:
            print(f"❌ 奖励领取API测试失败: {str(e)}")

        print("=" * 60)
        print("✅ 真实API调用测试完成")
        return True

    except Exception as e:
        print(f"❌ 真实API调用测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_complete_workflow():
    """测试完整工作流程"""
    print("🔄 开始完整工作流程测试...")

    auth_token = os.getenv('XIECHENG_TOKENS')
    if not auth_token:
        print("❌ 未设置 XIECHENG_TOKENS 环境变量")
        return False

    try:
        # 创建实例
        run = Run(
            app_name="携程旅行完整流程测试",
            app_env_name="XIECHENG_TOKENS",
            script_version="test"
        )

        print("🚀 执行完整自动化流程...")

        # 初始化并执行完整流程（签到 + 任务）
        run.init_vars()
        run.process(auth_token, 1)

        # 显示最终结果
        total_sign_points = run.base_point + run.extra_point + run.extra_ctrip_point
        total_all_points = total_sign_points + run.total_task_points

        print("\n📊 完整流程执行结果:")
        print(f"   连续签到: {run.continue_day} 天")
        print(f"   签到积分: {total_sign_points}")
        print(f"   任务积分: {run.total_task_points}")
        print(f"   总计积分: {total_all_points}")
        print(f"   完成任务: {len(run.completed_tasks)} 个")

        print("✅ 完整工作流程测试完成")
        return True

    except Exception as e:
        print(f"❌ 完整工作流程测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_error_scenarios():
    """测试错误场景"""
    print("🛡️  开始错误场景测试...")

    try:
        # 1. 测试无效token
        print("🔍 1. 测试无效token处理...")
        run = Run(
            app_name="携程错误测试",
            app_env_name="XIECHENG_TOKENS",
            script_version="test"
        )

        run.init_vars()
        run.process_vars("invalid_token_12345")

        # 尝试获取任务列表
        tasks = run.get_task_list()
        if not tasks:
            print("✅ 无效token正确处理：返回空任务列表")
        else:
            print("⚠️  无效token意外返回了任务")

        # 2. 测试网络错误处理
        print("🔍 2. 测试网络错误处理...")
        # 这里可以通过修改URL来模拟网络错误
        original_url = run.task_list_url
        run.task_list_url = "https://invalid-domain-12345.com/api"

        tasks = run.get_task_list()
        if not tasks:
            print("✅ 网络错误正确处理：返回空任务列表")
        else:
            print("⚠️  网络错误意外返回了任务")

        # 恢复原始URL
        run.task_list_url = original_url

        print("✅ 错误场景测试完成")
        return True

    except Exception as e:
        print(f"❌ 错误场景测试失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("🧪 携程旅行直接调用测试套件")
    print("=" * 80)

    # 检查环境变量
    auth_token = os.getenv('XIECHENG_TOKENS')
    if not auth_token:
        print("⚠️  未设置 XIECHENG_TOKENS 环境变量")
        print("💡 请设置: export XIECHENG_TOKENS='your_auth_token'")
        print("🔧 大部分测试将被跳过")
        print()

    # 执行测试
    tests = [
        ("真实API调用", test_real_api_calls),
        ("完整工作流程", test_complete_workflow),
        ("错误场景处理", test_error_scenarios),
    ]

    results = {}

    for test_name, test_func in tests:
        print(f"🧪 开始 {test_name} 测试...")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results[test_name] = False
        print()

    # 总结
    print("=" * 80)
    print("📊 直接调用测试结果:")

    passed = 0
    total = len(results)

    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
        if success:
            passed += 1

    print(f"\n📈 通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    print("=" * 80)

    if passed == total:
        print("🎉 所有直接调用测试通过！")
        print("\n💡 测试验证的功能:")
        print("   ✅ 真实API连接和响应")
        print("   ✅ 签到功能正常工作")
        print("   ✅ 任务获取和执行")
        print("   ✅ 奖励领取机制")
        print("   ✅ 完整自动化流程")
        print("   ✅ 错误处理机制")
    else:
        print("⚠️  部分测试未通过，请检查:")
        print("   1. XIECHENG_TOKENS 环境变量是否正确")
        print("   2. 网络连接是否正常")
        print("   3. 携程API是否有变化")

    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
