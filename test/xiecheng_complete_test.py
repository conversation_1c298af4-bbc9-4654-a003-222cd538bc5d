#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
携程旅行完整脚本执行测试
直接运行完整的携程旅行脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import subprocess
import time
from datetime import datetime


def test_complete_script_execution():
    """测试完整脚本执行"""
    print("🚀 开始完整脚本执行测试...")
    
    # 检查环境变量
    auth_token = os.getenv('XIECHENG_TOKENS')
    if not auth_token:
        print("❌ 未设置 XIECHENG_TOKENS 环境变量")
        print("💡 请设置: export XIECHENG_TOKENS='your_auth_token'")
        return False
    
    try:
        # 获取脚本路径
        script_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        script_path = os.path.join(script_dir, "xiecheng.py")
        
        if not os.path.exists(script_path):
            print(f"❌ 脚本文件不存在: {script_path}")
            return False
            
        print(f"📁 脚本路径: {script_path}")
        print(f"🔑 使用Token: {auth_token[:20]}...")
        print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 设置环境变量并执行脚本
        env = os.environ.copy()
        env['XIECHENG_TOKENS'] = auth_token
        
        print("\n" + "="*60)
        print("🎯 执行携程旅行完整脚本...")
        print("="*60)
        
        # 执行脚本
        start_time = time.time()
        result = subprocess.run(
            [sys.executable, script_path],
            env=env,
            capture_output=True,
            text=True,
            timeout=300  # 5分钟超时
        )
        end_time = time.time()
        
        execution_time = end_time - start_time
        
        print(f"\n⏱️  执行时间: {execution_time:.2f} 秒")
        print(f"🔄 返回码: {result.returncode}")
        
        # 显示输出
        if result.stdout:
            print("\n📤 标准输出:")
            print("-" * 40)
            print(result.stdout)
            print("-" * 40)
            
        if result.stderr:
            print("\n📥 错误输出:")
            print("-" * 40)
            print(result.stderr)
            print("-" * 40)
            
        # 分析结果
        success = result.returncode == 0
        
        if success:
            print("✅ 完整脚本执行成功")
            
            # 分析输出内容
            output = result.stdout
            if "签到成功" in output:
                print("   ✅ 签到功能正常")
            if "任务" in output and "积分" in output:
                print("   ✅ 任务功能正常")
            if "获得" in output and "积分" in output:
                print("   ✅ 积分获取正常")
                
        else:
            print("❌ 完整脚本执行失败")
            
        return success
        
    except subprocess.TimeoutExpired:
        print("❌ 脚本执行超时（5分钟）")
        return False
    except Exception as e:
        print(f"❌ 完整脚本执行异常: {str(e)}")
        return False


def test_script_with_multiple_accounts():
    """测试多账号脚本执行"""
    print("👥 开始多账号脚本执行测试...")
    
    # 检查环境变量
    auth_tokens = os.getenv('XIECHENG_TOKENS')
    if not auth_tokens:
        print("❌ 未设置 XIECHENG_TOKENS 环境变量")
        return False
    
    # 检查是否有多个账号
    tokens = [token.strip() for token in auth_tokens.split('\n') if token.strip()]
    
    if len(tokens) == 1:
        print("ℹ️  只有一个账号，执行单账号测试")
        return test_complete_script_execution()
    
    try:
        print(f"📊 检测到 {len(tokens)} 个账号")
        
        # 获取脚本路径
        script_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        script_path = os.path.join(script_dir, "xiecheng.py")
        
        # 设置环境变量并执行脚本
        env = os.environ.copy()
        env['XIECHENG_TOKENS'] = auth_tokens
        
        print("\n" + "="*60)
        print("🎯 执行携程旅行多账号脚本...")
        print("="*60)
        
        # 执行脚本
        start_time = time.time()
        result = subprocess.run(
            [sys.executable, script_path],
            env=env,
            capture_output=True,
            text=True,
            timeout=600  # 10分钟超时（多账号需要更长时间）
        )
        end_time = time.time()
        
        execution_time = end_time - start_time
        
        print(f"\n⏱️  总执行时间: {execution_time:.2f} 秒")
        print(f"👥 处理账号数: {len(tokens)}")
        print(f"⚡ 平均每账号: {execution_time/len(tokens):.2f} 秒")
        print(f"🔄 返回码: {result.returncode}")
        
        # 显示输出
        if result.stdout:
            print("\n📤 执行日志:")
            print("-" * 40)
            print(result.stdout)
            print("-" * 40)
            
        success = result.returncode == 0
        
        if success:
            print("✅ 多账号脚本执行成功")
            
            # 分析每个账号的执行情况
            output = result.stdout
            account_count = output.count("开始处理账号")
            success_count = output.count("签到成功")
            
            print(f"   📊 处理账号: {account_count}")
            print(f"   ✅ 成功账号: {success_count}")
            print(f"   📈 成功率: {success_count/account_count*100:.1f}%" if account_count > 0 else "   📈 成功率: N/A")
            
        else:
            print("❌ 多账号脚本执行失败")
            
        return success
        
    except subprocess.TimeoutExpired:
        print("❌ 多账号脚本执行超时（10分钟）")
        return False
    except Exception as e:
        print(f"❌ 多账号脚本执行异常: {str(e)}")
        return False


def test_script_performance():
    """测试脚本性能"""
    print("⚡ 开始脚本性能测试...")
    
    auth_token = os.getenv('XIECHENG_TOKENS')
    if not auth_token:
        print("❌ 未设置 XIECHENG_TOKENS 环境变量")
        return False
    
    try:
        # 执行多次测试
        execution_times = []
        success_count = 0
        test_rounds = 3
        
        print(f"🔄 执行 {test_rounds} 轮性能测试...")
        
        for i in range(test_rounds):
            print(f"\n🧪 第 {i+1} 轮测试...")
            
            # 获取脚本路径
            script_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            script_path = os.path.join(script_dir, "xiecheng.py")
            
            # 设置环境变量
            env = os.environ.copy()
            env['XIECHENG_TOKENS'] = auth_token.split('\n')[0]  # 只用第一个账号
            
            # 执行脚本
            start_time = time.time()
            result = subprocess.run(
                [sys.executable, script_path],
                env=env,
                capture_output=True,
                text=True,
                timeout=180  # 3分钟超时
            )
            end_time = time.time()
            
            execution_time = end_time - start_time
            execution_times.append(execution_time)
            
            if result.returncode == 0:
                success_count += 1
                print(f"   ✅ 第 {i+1} 轮成功，耗时: {execution_time:.2f} 秒")
            else:
                print(f"   ❌ 第 {i+1} 轮失败，耗时: {execution_time:.2f} 秒")
                
            # 轮次间隔
            if i < test_rounds - 1:
                print("   ⏳ 等待 10 秒后进行下一轮...")
                time.sleep(10)
        
        # 性能统计
        if execution_times:
            avg_time = sum(execution_times) / len(execution_times)
            min_time = min(execution_times)
            max_time = max(execution_times)
            
            print(f"\n📊 性能测试结果:")
            print(f"   🎯 成功率: {success_count}/{test_rounds} ({success_count/test_rounds*100:.1f}%)")
            print(f"   ⚡ 平均耗时: {avg_time:.2f} 秒")
            print(f"   🚀 最快耗时: {min_time:.2f} 秒")
            print(f"   🐌 最慢耗时: {max_time:.2f} 秒")
            
            # 性能评估
            if avg_time < 30:
                print("   🏆 性能评级: 优秀")
            elif avg_time < 60:
                print("   👍 性能评级: 良好")
            elif avg_time < 120:
                print("   👌 性能评级: 一般")
            else:
                print("   ⚠️  性能评级: 需要优化")
                
        return success_count == test_rounds
        
    except Exception as e:
        print(f"❌ 性能测试异常: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("🧪 携程旅行完整脚本执行测试套件")
    print("=" * 80)
    
    # 检查环境变量
    auth_token = os.getenv('XIECHENG_TOKENS')
    if not auth_token:
        print("⚠️  未设置 XIECHENG_TOKENS 环境变量")
        print("💡 请设置: export XIECHENG_TOKENS='your_auth_token'")
        print("🔧 所有测试将被跳过")
        return False
    
    # 执行测试
    tests = [
        ("完整脚本执行", test_complete_script_execution),
        ("多账号脚本执行", test_script_with_multiple_accounts),
        ("脚本性能测试", test_script_performance),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🧪 开始 {test_name} 测试...")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results[test_name] = False
        print()
    
    # 总结
    print("=" * 80)
    print("📊 完整脚本测试结果:")
    
    passed = 0
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n📈 通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    print("=" * 80)
    
    if passed == total:
        print("🎉 所有完整脚本测试通过！")
        print("\n💡 验证的功能:")
        print("   ✅ 脚本可以正常启动和运行")
        print("   ✅ 环境变量正确读取")
        print("   ✅ 签到和任务功能正常")
        print("   ✅ 多账号处理正常")
        print("   ✅ 脚本性能稳定")
        print("   ✅ 错误处理机制有效")
    else:
        print("⚠️  部分测试未通过，请检查:")
        print("   1. 脚本文件是否存在")
        print("   2. 环境变量是否正确设置")
        print("   3. 网络连接是否正常")
        print("   4. 携程API是否可用")
        
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
