import requests
import json

# 携程旅行



url = "https://m.ctrip.com/restapi/soa2/22769/signToday"

XIECHENG_TOKENS="D76A9DC1CA096D44F1A34BA85E0E2ADDD007F8E799125F155C7A216D1C665337";

payload = {
  "platform": "APP",
  "openId": "",
  "rmsToken": "",
  "head": {
    "cid": "32001078290367030195",
    "ctok": "",
    "cver": "881.006",
    "lang": "01",
    "sid": "8913",
    "syscode": "32",
    "auth": XIECHENG_TOKENS,
    "xsid": "",
    "extension": []
  }
}

headers = {
  'User-Agent': "Mozilla/5.0 (Linux; Android 15; 23013RK75C Build/AQ3A.240912.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/137.0.7151.88 Mobile Safari/537.36_CtripAPP_Android_8.81.6_eb64_Ctrip_CtripWireless_8.81.6_cDevice=23013RK75C_cSize=w1440*h3080__v=881.006_os=Android_osv=15_m=23013RK75C_brand=Redmi_vg=5_safeAreaTop=34_safeAreaBottom=0",
  'Accept-Encoding': "gzip, deflate, br, zstd",
  'Content-Type': "application/json",
  'sec-ch-ua-platform': "\"Android\"",
  'cookieorigin': "https://m.ctrip.com",
  'sec-ch-ua': "\"Android WebView\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
  'sec-ch-ua-mobile': "?1",
  'origin': "https://m.ctrip.com",
  'x-requested-with': "ctrip.android.view",
  'sec-fetch-site': "same-origin",
  'sec-fetch-mode': "cors",
  'sec-fetch-dest': "empty",
  'referer': "https://m.ctrip.com/activitysetupapp/mkt/index/membersignin2021?isHideNavBar=YES&pushcode=mypoint&from_native_page=1&fromMemberTab=point",
  'accept-language': "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7",
  'priority': "u=1, i"
}

response = requests.post(url, data=json.dumps(payload), headers=headers)

print(response.text)
# 重复签到 {"ResponseStatus":{"Timestamp":"/Date(1750084048466+0800)/","Ack":"Success","Errors":[],"Extension":[{"Id":"CLOGGING_TRACE_ID","Value":"2380009157860327257"},{"Id":"RootMessageId","Value":"100025527-0a3937a5-486134-1234568"}]},"code":400001,"message":"当日已签到，无法补签","continueDay":0,"baseIntegratedPoint":0,"extraIntegratedPoint":0,"extraCtripPoint":0,"tomorrowIntegratedPoint":0,"extraDay":0,"type":0,"lotteryTimes":0,"singVersion":0,"lottery":0}
#签到成功{"ResponseStatus":{"Timestamp":"/Date(1750181762695+0800)/","Ack":"Success","Errors":[],"Extension":[{"Id":"CLOGGING_TRACE_ID","Value":"7949464003413446731"},{"Id":"RootMessageId","Value":"100025527-0a91c91c-486161-1112239"}]},"code":0,"message":"SUCCESS","continueDay":1,"baseIntegratedPoint":2,"extraIntegratedPoint":0,"extraCtripPoint":0,"tomorrowIntegratedPoint":4,"extraDay":0,"type":0,"lotteryTimes":0,"singVersion":1,"lottery":0}
#continueDay 连续签到时间
#baseIntegratedPoint 获得积分
#extraIntegratedPoint 获得额外积分
#extraCtripPoint 获得额外携程积分
#tomorrowIntegratedPoint 明天可以获得的积分
#extraDay 获得额外积分的天数
