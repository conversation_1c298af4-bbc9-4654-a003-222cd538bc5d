import requests

#1. 天星借钱 周周抽奖列表
url = "https://api.jr.airstarfinance.net/ca/userRightsWeek/homeDetail?code=&device=4&imei=&lon=&lat=&app=com.mipay.wallet&cUserId=Ia2-L5qu3xu-2Z1_lersy1r22iE"

headers = {
  'User-Agent': "Mozilla/5.0 (Linux; U; Android 15; zh-CN; 23013RK75C Build/AQ3A.240912.001; AppBundle/com.mipay.wallet; AppVersionName/6.91.1.5302.2333; AppVersionCode/20577603; MiuiVersion/stable-OS2.0.9.0.VMNCNXM; DeviceId/mondrian; NetworkType/WIFI; mix_version; WebViewVersion/136.0.7103.125) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.36 XiaoMi/MiuiBrowser/4.3",
  'Accept-Encoding': "gzip, deflate, br, zstd",
  'content-length': "0",
  'sec-ch-ua-platform': "\"Android\"",
  'sec-ch-ua': "\"Chromium\";v=\"136\", \"Android WebView\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
  'sec-ch-ua-mobile': "?1",
  'origin': "https://api.jr.airstarfinance.net",
  'x-requested-with': "com.mipay.wallet",
  'sec-fetch-site': "same-origin",
  'sec-fetch-mode': "cors",
  'sec-fetch-dest': "empty",
  'referer': "https://api.jr.airstarfinance.net/loan/newActivity/202208/userRights/index.html?from=QB&_transparentNaviBar=true&_noDarkMode=true&cUserId=Ia2-L5qu3xu-2Z1_lersy1r22iE&_statusBarHeight=120",
  'accept-language': "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
  'priority': "u=1, i",
  'Cookie': "cUserId=Ia2-L5qu3xu-2Z1_lersy1r22iE; jrairstar_serviceToken=vKCn0n6r/Nnnc/5JJfAyM8+cTvjKzmEnwqASVVMXXO0BSk8PrE6D4ibCp7b4MOybWoPMS5d3U6SY7yfwZHS1BMcLipZg9y+I+q5mIqlseokslUf6xXjG+dDhaxOkXlfNoxkHWKvOci85I2HXHjbPQY29AVpAc6LJYkzCeFqbnT/DDN0FK48OOMaVrStebFX7nn4k44MeaNiFIv+NX2DTNw==; jrairstar_ph=oD1gBeutszExghoXwW9Byw==; sajssdk_2015_cross_new_user=1; jrairstar_slh=FPe2pFr7eTcZihMnSrrDljoCsCY=; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22Ia2-L5qu3xu-2Z1_lersy1r22iE%22%2C%22first_id%22%3A%221974f4c9d7b26-09390783d3e4c-54f3003-376980-1974f4c9d7c6c2%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfbG9naW5faWQiOiJJYTItTDVxdTN4dS0yWjFfbGVyc3kxcjIyaUUiLCIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk3NGY0YzlkN2IyNi0wOTM5MDc4M2QzZTRjLTU0ZjMwMDMtMzc2OTgwLTE5NzRmNGM5ZDdjNmMyIn0%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%22Ia2-L5qu3xu-2Z1_lersy1r22iE%22%7D%2C%22%24device_id%22%3A%221974f4c9d7b26-09390783d3e4c-54f3003-376980-1974f4c9d7c6c2%22%7D"
}

response = requests.post(url, headers=headers)

print(response.text)
# 只取响应中的 title 中有 无门槛 三个字的任务
# {"success":true,"code":0,"name":"Success","result":"OK","data":{"currentActivityPage":{"mainImg":"https://ts.market.mi-img.com/thumbnail/png/l/","drawBaseImg":"https://ts.market.mi-img.com/thumbnail/png/l/","drawButtonImg":"https://ts.market.mi-img.com/thumbnail/png/l/Finance/0fd9b8729c37a4b8192fc314662d41f93dced6746","detailHeaderImg":"https://ts.market.mi-img.com/thumbnail/png/l/","bgColor":"#5d13e4","bgImg":"https://ts.market.mi-img.com/thumbnail/png/l/Finance/000abf256b9324f94b2514b71bcad4c7bbfe898ad"},"taskInfo":{"chanceCount":0,"signInToday":false,"payTaskStatus":0,"payAmountLimit":0,"tasks":[{"activityId":458,"userId":"184956742","taskId":76792843,"type":3,"status":0,"title":"薅羊毛区%%无门槛%%","subTitle":"完成“周周赢豪礼”签到","relatedUrl":"","popupText":"","payChanceType":0,"countdown":0,"prizeDTO":{"prizeId":1117,"code":"week_143_value3","name":"小米小爱音箱Play 增强版","image":"https://ts.market.mi-img.com/thumbnail/png/l/Finance/0c80f553c30d0427da7d22f37b7acafb5ba72d0cc","hitImage":"https://ts.market.mi-img.com/thumbnail/png/l/Finance/07fa8bdad9a4d48e2821dc13fcba9ea05ba6bbef7"},"applyState":0,"userCount":8155,"hit":false},{"activityId":458,"userId":"184956742","taskId":76792844,"type":17,"status":0,"title":"填写问卷%%性价比%%","subTitle":"反馈“天星借钱”使用体验","relatedUrl":"","popupText":"","payChanceType":0,"questionnaireDTOS":[{"questionUrl":"https://m.beehive.miui.com/-FNewbVfzN-02bkD_z2SJw","answered":false,"questionName":"“天星借钱”问卷","chanceCount":1,"active":true}],"countdown":0,"prizeDTO":{"prizeId":1116,"code":"week_143_value17","name":"REDMI Buds 6 Pro","image":"https://ts.market.mi-img.com/thumbnail/png/l/Finance/0a01e8e89e0b74166b17e6680e96a696c5b859867","hitImage":"https://ts.market.mi-img.com/thumbnail/png/l/Finance/09143d7de177e41dcbac852e0b4828b02d6426357"},"applyState":0,"userCount":4305,"hit":false},{"activityId":458,"userId":"184956742","taskId":76792845,"type":15,"status":0,"title":"米家旗舰区%%轻松抽%%","subTitle":"“天星借钱”完成任意金额借款","relatedUrl":"","popupText":"","payChanceType":1,"countdown":0,"prizeDTO":{"prizeId":1115,"code":"week_143_value15","name":"REDMI K80（16GB+512GB）","image":"https://ts.market.mi-img.com/thumbnail/png/l/Finance/0d6e1ef7ead2d491fa484f204bb4c97bebeb2c423","hitImage":"https://ts.market.mi-img.com/thumbnail/png/l/Finance/067612618f8794230ac7966c762af1e432ac5818c"},"applyState":0,"userCount":360,"hit":false}],"newChanceCount":0,"luckyDrawStart":false,"luckyDrawStartTime":0},"activity":{"id":458,"code":"week_143_20250609","startTime":1749398400000,"endTime":1750003199000,"activityEndMills":259756289},"lastActivityId":457,"isJumpLastWeek":true,"luckyDrawState":1,"hitPrizeCount":8},"description":"成功","message":""}


#2. 天星借钱 获取抽奖报名机会
import requests

url = "https://api.jr.airstarfinance.net/ca/luckyDraw/fetchChance"

payload = {
  'taskId': "76792843",
  'activityCode': "week_143_20250609",
  'app': "com.mipay.wallet",
  'cUserId': "Ia2-L5qu3xu-2Z1_lersy1r22iE"
}

headers = {
  'User-Agent': "Mozilla/5.0 (Linux; U; Android 15; zh-CN; 23013RK75C Build/AQ3A.240912.001; AppBundle/com.mipay.wallet; AppVersionName/6.92.0.5375.2592; AppVersionCode/20577606; MiuiVersion/stable-OS2.0.9.0.VMNCNXM; DeviceId/mondrian; NetworkType/WIFI; mix_version; WebViewVersion/136.0.7103.125) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.36 XiaoMi/MiuiBrowser/4.3",
  'Accept-Encoding': "gzip, deflate, br, zstd",
  'sec-ch-ua-platform': "\"Android\"",
  'sec-ch-ua': "\"Chromium\";v=\"136\", \"Android WebView\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
  'sec-ch-ua-mobile': "?1",
  'origin': "https://api.jr.airstarfinance.net",
  'x-requested-with': "com.mipay.wallet",
  'sec-fetch-site': "same-origin",
  'sec-fetch-mode': "cors",
  'sec-fetch-dest': "empty",
  'referer': "https://api.jr.airstarfinance.net/loan/newActivity/202208/userRights/index.html?from=QB&_transparentNaviBar=true&_noDarkMode=true&cUserId=Ia2-L5qu3xu-2Z1_lersy1r22iE&_statusBarHeight=120",
  'accept-language': "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
  'priority': "u=1, i",
  'Cookie': "cUserId=Ia2-L5qu3xu-2Z1_lersy1r22iE; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22Ia2-L5qu3xu-2Z1_lersy1r22iE%22%2C%22first_id%22%3A%221974f4c9d7b26-09390783d3e4c-54f3003-376980-1974f4c9d7c6c2%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfbG9naW5faWQiOiJJYTItTDVxdTN4dS0yWjFfbGVyc3kxcjIyaUUiLCIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk3NGY0YzlkN2IyNi0wOTM5MDc4M2QzZTRjLTU0ZjMwMDMtMzc2OTgwLTE5NzRmNGM5ZDdjNmMyIn0%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%22Ia2-L5qu3xu-2Z1_lersy1r22iE%22%7D%2C%22%24device_id%22%3A%221974f4c9d7b26-09390783d3e4c-54f3003-376980-1974f4c9d7c6c2%22%7D; jrairstar_serviceToken=vKCn0n6r/Nnnc/5JJfAyM4/ZJ27TjuckKafb4MWJbb9kJRQxJYgeENGxpu/tkJFaHTEEINURN0OFz6jXNPqdnA0IYIVuuEDvEEgEVGFULjEb+4esxFbyExJiMqBhTs3+CNoITw+umuk17leMj4aIuOcutv2sosm7od7ObHYhWUtXtXnQiWOwvCdjBTJIzm8nJHSbpbpPmJs+YPS20Qhj2A==; jrairstar_ph=XvqaipqB0HWBb6uGiJkFew==; jrairstar_slh=1A45B0QFokFPXv6tLxDSe5Cqr3k="
}

response = requests.post(url, data=payload, headers=headers)

print(response.text)
# chanceCount = 1 代表有机会
# {"success":true,"code":0,"name":"Success","result":"OK","data":{"chanceCount":1},"description":"成功","message":""}

#3. 天星借钱 抽奖报名
import requests

url = "https://api.jr.airstarfinance.net/ca/userRightsWeek/applyLuckyDraw?type=9&prizeId=1117&taskId=76792843&app=com.mipay.wallet&cUserId=Ia2-L5qu3xu-2Z1_lersy1r22iE"

headers = {
  'User-Agent': "Mozilla/5.0 (Linux; U; Android 15; zh-CN; 23013RK75C Build/AQ3A.240912.001; AppBundle/com.mipay.wallet; AppVersionName/6.92.0.5375.2592; AppVersionCode/20577606; MiuiVersion/stable-OS2.0.9.0.VMNCNXM; DeviceId/mondrian; NetworkType/WIFI; mix_version; WebViewVersion/136.0.7103.125) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.36 XiaoMi/MiuiBrowser/4.3",
  'Accept-Encoding': "gzip, deflate, br, zstd",
  'content-length': "0",
  'sec-ch-ua-platform': "\"Android\"",
  'sec-ch-ua': "\"Chromium\";v=\"136\", \"Android WebView\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
  'sec-ch-ua-mobile': "?1",
  'origin': "https://api.jr.airstarfinance.net",
  'x-requested-with': "com.mipay.wallet",
  'sec-fetch-site': "same-origin",
  'sec-fetch-mode': "cors",
  'sec-fetch-dest': "empty",
  'referer': "https://api.jr.airstarfinance.net/loan/newActivity/202208/userRights/index.html?from=QB&_transparentNaviBar=true&_noDarkMode=true&cUserId=Ia2-L5qu3xu-2Z1_lersy1r22iE&_statusBarHeight=120",
  'accept-language': "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
  'priority': "u=1, i",
  'Cookie': "cUserId=Ia2-L5qu3xu-2Z1_lersy1r22iE; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22Ia2-L5qu3xu-2Z1_lersy1r22iE%22%2C%22first_id%22%3A%221974f4c9d7b26-09390783d3e4c-54f3003-376980-1974f4c9d7c6c2%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfbG9naW5faWQiOiJJYTItTDVxdTN4dS0yWjFfbGVyc3kxcjIyaUUiLCIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk3NGY0YzlkN2IyNi0wOTM5MDc4M2QzZTRjLTU0ZjMwMDMtMzc2OTgwLTE5NzRmNGM5ZDdjNmMyIn0%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%22Ia2-L5qu3xu-2Z1_lersy1r22iE%22%7D%2C%22%24device_id%22%3A%221974f4c9d7b26-09390783d3e4c-54f3003-376980-1974f4c9d7c6c2%22%7D; jrairstar_serviceToken=vKCn0n6r/Nnnc/5JJfAyM4/ZJ27TjuckKafb4MWJbb9kJRQxJYgeENGxpu/tkJFaHTEEINURN0OFz6jXNPqdnA0IYIVuuEDvEEgEVGFULjEb+4esxFbyExJiMqBhTs3+CNoITw+umuk17leMj4aIuOcutv2sosm7od7ObHYhWUtXtXnQiWOwvCdjBTJIzm8nJHSbpbpPmJs+YPS20Qhj2A==; jrairstar_ph=XvqaipqB0HWBb6uGiJkFew==; jrairstar_slh=1A45B0QFokFPXv6tLxDSe5Cqr3k="
}

response = requests.post(url, headers=headers)

print(response.text)
# Success 为成功
# {"success":true,"code":0,"name":"Success","result":"OK","description":"成功","message":""}
