import requests
import json

XIECHENG_TOKENS="D76A9DC1CA096D44F1A34BA85E0E2ADDD007F8E799125F155C7A216D1C665337";

#1. 任务列表
url = "https://m.ctrip.com/restapi/soa2/22598/userTaskList?_fxpcqlniredt=32001078290367030195&x-traceID=32001078290367030195-1750256951379-9256425"

payload = {
  "channelCode": "2H3294O46M",
  "extMap": {
    "mktTaskSort": ""
  },
  "oAuthHead": {},
  "platform": "APP",
  "rmsToken": "",
  "version": "1",
  "osType": "android",
  "appVersion": "8.81.6",
  "subOsType": "xiaomi",
  "_locale": "zh-CN",
  "head": {
    "cid": "32001078290367030195",
    "ctok": "",
    "cver": "881.006",
    "lang": "01",
    "sid": "8913",
    "syscode": "32",
    "auth": XIECHENG_TOKENS,
    "xsid": "",
    "extension": []
  }
}

headers = {
  'User-Agent': "Mozilla/5.0 (Linux; Android 15; 23013RK75C Build/AQ3A.240912.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/137.0.7151.88 Mobile Safari/537.36_CtripAPP_Android_8.81.6_eb64_Ctrip_CtripWireless_8.81.6_cDevice=23013RK75C_cSize=w1440*h3080__v=881.006_os=Android_osv=15_m=23013RK75C_brand=Redmi_vg=5_safeAreaTop=34_safeAreaBottom=0",
  'Accept-Encoding': "gzip, deflate, br, zstd",
  'Content-Type': "application/json",
  'sec-ch-ua-platform': "\"Android\"",
  'cookieorigin': "https://contents.ctrip.com",
  'sec-ch-ua': "\"Android WebView\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
  'sec-ch-ua-mobile': "?1",
  'origin': "https://contents.ctrip.com",
  'x-requested-with': "ctrip.android.view",
  'sec-fetch-site': "same-site",
  'sec-fetch-mode': "cors",
  'sec-fetch-dest': "empty",
  'referer': "https://contents.ctrip.com/",
  'accept-language': "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7",
  'Cookie': f"cticket={XIECHENG_TOKENS}"
}

response = requests.post(url, data=json.dumps(payload), headers=headers)

print(response.text)
# 每个任务在响应的 json 中的路径 /todoTaskList/0/id
# 只做 eventType 为 NO_REPEAT_BROWSE 的任务
# 其中一个任务的示例数据：{"id":7442,"status":3,"process":1,"receivedTime":1750181945000,"deadline":1750262400000,"changeTime":1750256137000,"eventId":57,"eventType":"NO_REPEAT_BROWSE","eventName":"去重浏览","eventTarget":1,"eventCondition":"{\"_mktTaskCountTimes\":1,\"_mktTaskActivityId\":\"task7442\"}","eventDisplay":"{\"_taskTimes\":15,\"_taskPopType\":201,\"urlH5WakeupWxMp\":false}","browseSeconds":0,"currencyDtoList":[{"id":1,"name":"积分","worth":0.01,"number":1,"type":1,"awardNumberType":1,"minNumber":0,"maxNumber":0,"icon":"https://pages.c-ctrip.com/union/game/task/jifen.png"}],"awardDesc":"+1积分","extendJson":"","internalName":"TA引流任务-去重浏览","sort":100,"validityStartTime":1689782400000,"validityEndTime":1924790400000,"category":3,"cyclePeriod":1,"displayName":"浏览猫途鹰旅行产品","receiveEndTime":1924790400000,"todoStartTime":1689782400000,"todoEndTime":1924790400000,"buttonText":"去猫途鹰","icon":"https://dimg04.c-ctrip.com/images/02Y3512000fbacawg95A8.png","description":"浏览15s","h5Url":"","appUrl":"https://m.tripadvisor.cn/webapp/toplist/couponMarketing?wakeupApp=0&activityId=3&m=23072101&_mktTaskActivityId=task7442&sepopup=201&inpopup=true&mktTaskSecondId&mktTaskSecondType=&innersid=task7442&innerouid=task&mktTaskStatus=3","wechatUrl":"","speedAppUrl":"https://m.tripadvisor.cn/webapp/toplist/couponMarketing?wakeupApp=0&activityId=3&m=23072101&_mktTaskActivityId=task7442&sepopup=201&inpopup=true&mktTaskSecondId&mktTaskSecondType=&innersid=task7442&innerouid=task&mktTaskStatus=3","alipayMpUrl":"","baiduMpUrl":"","fastAppUrl":"","touTiaoMpUrl":"","qqMpUrl":"","needReceive":0,"locked":0,"userLockedStatus":0,"awardType":1,"itemList":[],"osType":"","subOsType":"","appMinVersion":"","appMinVersionMatch":1,"finishJump":1,"taskGroupDto":{"id":302,"name":"TA","type":2,"sort":1,"currentProcess":1,"totalProcess":1},"taskDisplayGroupDto":{"id":295,"name":"合作任务","type":5,"sort":1,"currentProcess":0,"totalProcess":47},"showViewRewardButton":0,"slidingBlock":0,"businessSite":"","buttonDoneText":"已完成","buttonAwardText":"领奖励","completeTime":1750181953000,"awardTime":1750256137000,"endCountdown":0,"buttonReceiveText":"领任务","limitTime":0,"buttonLimitTimeText":"已失效","forbidBatchReceive":0,"openReceiveTaskExtend":0,"ruleDesc":"","displayAppInfo":"","displayAppStatus":1}
# status:3 完成，status:0 未开始

#做任务
url = "https://m.ctrip.com/restapi/soa2/22598/todoTask?_fxpcqlniredt=32001078290367030195&x-traceID=32001078290367030195-1750004515359-7384586"

payload = {
  "channelCode": "2H3294O46M",
  "taskId": 7442,
  "status": 0,
  "done": 0,
  "oAuthHead": {},
  "platform": "APP",
  "rmsToken": "",
  "version": "1",
  "osType": "android",
  "appVersion": "8.81.6",
  "subOsType": "xiaomi",
  "_locale": "zh-CN",
  "allianceid": "5112619",
  "sid": "101281958",
  "ouid": "",
  "sourceid": "",
  "pushcode": "membersign",
  "innersid": "",
  "innerouid": "",
  "allianceInfo": {
    "aid": "5112619",
    "sid": "101281958",
    "ouid": "",
    "pushCode": "membersign",
    "pageId": ""
  },
  "head": {
    "cid": "32001078290367030195",
    "ctok": "",
    "cver": "881.006",
    "lang": "01",
    "sid": "8913",
    "syscode": "32",
    "auth": XIECHENG_TOKENS,
    "xsid": "",
    "extension": []
  }
}

headers = {
  'User-Agent': "Mozilla/5.0 (Linux; Android 15; 23013RK75C Build/AQ3A.240912.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/137.0.7151.88 Mobile Safari/537.36_CtripAPP_Android_8.81.6_eb64_Ctrip_CtripWireless_8.81.6_cDevice=23013RK75C_cSize=w1440*h3080__v=881.006_os=Android_osv=15_m=23013RK75C_brand=Redmi_vg=5_safeAreaTop=34_safeAreaBottom=0",
  'Accept-Encoding': "gzip, deflate, br, zstd",
  'Content-Type': "application/json",
  'sec-ch-ua-platform': "\"Android\"",
  'cookieorigin': "https://contents.ctrip.com",
  'sec-ch-ua': "\"Android WebView\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
  'sec-ch-ua-mobile': "?1",
  'origin': "https://contents.ctrip.com",
  'x-requested-with': "ctrip.android.view",
  'sec-fetch-site': "same-site",
  'sec-fetch-mode': "cors",
  'sec-fetch-dest': "empty",
  'referer': "https://contents.ctrip.com/",
  'accept-language': "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7",
  'priority': "u=1, i",
  'Cookie': f"_cticket={XIECHENG_TOKENS}"
}

response = requests.post(url, data=json.dumps(payload), headers=headers)

print(response.text)
#成功 {"ResponseStatus":{"Timestamp":"/Date(1750182001878+0800)/","Ack":"Success","Errors":[],"Extension":[{"Id":"CLOGGING_TRACE_ID","Value":"7293867073172029171"},{"Id":"RootMessageId","Value":"100025527-0a18f6db-486161-1220677"}]},"code":200,"message":"SUCCESS","infoMap":{"receivedTaskTime":"1750181945000","receivedTaskId":"365456925"}}

url = "https://m.ctrip.com/restapi/soa2/22598/taskBrowseDone"

payload = {
  "_taskDetailId": "https://m.ctrip.com/webapp/you/hotspot/channel?source=qiandaozhongxin&_mktTaskActivityId=task946&sepopup=201&inpopup=true&mktTaskSecondId&mktTaskSecondType=&innersid=task12230&innerouid=task&mktTaskStatus=0&from_native_page=1",
  "_mktTaskActivityId": "task7442",
  "head": {
    "auth": XIECHENG_TOKENS
  }
}

headers = {
  'User-Agent': "Mozilla/5.0 (Linux; Android 15; 23013RK75C Build/AQ3A.240912.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/137.0.7151.88 Mobile Safari/537.36_CtripAPP_Android_8.81.6_eb64_Ctrip_CtripWireless_8.81.6_cDevice=23013RK75C_cSize=w1440*h3080__v=881.006_os=Android_osv=15_m=23013RK75C_brand=Redmi_vg=5_safeAreaTop=34_safeAreaBottom=0",
  'Accept-Encoding': "gzip, deflate, br, zstd",
  'Content-Type': "application/json",
  'sec-ch-ua-platform': "\"Android\"",
  'cookieorigin': "https://m.ctrip.com",
  'sec-ch-ua': "\"Android WebView\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
  'sec-ch-ua-mobile': "?1",
  'origin': "https://m.ctrip.com",
  'x-requested-with': "ctrip.android.view",
  'sec-fetch-site': "same-origin",
  'sec-fetch-mode': "cors",
  'sec-fetch-dest': "empty",
  'referer': "https://m.ctrip.com/webapp/you/hotspot/channel?source=qiandaozhongxin&_mktTaskActivityId=task12230&sepopup=201&inpopup=true&mktTaskSecondId&mktTaskSecondType=&innersid=task12230&innerouid=task&mktTaskStatus=0&from_native_page=1",
  'accept-language': "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7",
  'priority': "u=1, i",
  'Cookie': f"_cticket={XIECHENG_TOKENS}"
}

response = requests.post(url, data=json.dumps(payload), headers=headers)

print(response.text)
# {"ResponseStatus":{"Timestamp":"/Date(1750255529612+0800)/","Ack":"Success","Errors":[],"Extension":[{"Id":"CLOGGING_TRACE_ID","Value":"6526573935218830558"},{"Id":"RootMessageId","Value":"100025527-0a183149-486182-362298"}]},"code":401005,"message":"该项目已经浏览过了!"}


# 4. 领取任务奖励
url = "https://m.ctrip.com/restapi/soa2/22769/batchAwardTask?_fxpcqlniredt=32001078290367030195&x-traceID=32001078290367030195-1750256137014-1345768"

payload = {
  "taskIdList": [
    12230,
    10011,
    1828,
    7442
  ],
  "rmsToken": "fp=283247-CCED8C-319A47&vid=1731143856193.22ddFqDuhVxJ&pageId=10650086111&r=6e2e73f370cd4dfd904a158f15518140&ip=***************&rg=fin&screen=412x915&tz=+8&blang=en-US&oslang=en-US&ua=Mozilla%2F5.0%20(Linux%3B%20Android%2015%3B%2023013RK75C%20Build%2FAQ3A.240912.001%3B%20wv)%20AppleWebKit%2F537.36%20(KHTML%2C%20like%20Gecko)%20Version%2F4.0%20Chrome%2F137.0.7151.88%20Mobile%20Safari%2F537.36_CtripAPP_Android_8.81.6_eb64_Ctrip_CtripWireless_8.81.6_cDevice%3D23013RK75C_cSize%3Dw1440*h3080__v%3D881.006_os%3DAndroid_osv%3D15_m%3D23013RK75C_br&v=m17&bl=true&clientid=32001078290367030195&ftoken=",
  "platform": "APP",
  "head": {
    "cid": "32001078290367030195",
    "ctok": "",
    "cver": "881.006",
    "lang": "01",
    "sid": "8913",
    "syscode": "32",
    "auth": "D76A9DC1CA096D44F1A34BA85E0E2ADDD007F8E799125F155C7A216D1C665337",
    "xsid": "",
    "extension": []
  }
}

headers = {
  'User-Agent': "Mozilla/5.0 (Linux; Android 15; 23013RK75C Build/AQ3A.240912.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/137.0.7151.88 Mobile Safari/537.36_CtripAPP_Android_8.81.6_eb64_Ctrip_CtripWireless_8.81.6_cDevice=23013RK75C_cSize=w1440*h3080__v=881.006_os=Android_osv=15_m=23013RK75C_brand=Redmi_vg=5_safeAreaTop=34_safeAreaBottom=0",
  'Accept-Encoding': "gzip, deflate, br, zstd",
  'Content-Type': "application/json",
  'sec-ch-ua-platform': "\"Android\"",
  'cookieorigin': "https://contents.ctrip.com",
  'sec-ch-ua': "\"Android WebView\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
  'sec-ch-ua-mobile': "?1",
  'origin': "https://contents.ctrip.com",
  'x-requested-with': "ctrip.android.view",
  'sec-fetch-site': "same-site",
  'sec-fetch-mode': "cors",
  'sec-fetch-dest': "empty",
  'referer': "https://contents.ctrip.com/",
  'accept-language': "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7",
  'priority': "u=1, i",
  'Cookie': f"_cticket={XIECHENG_TOKENS}"
}

response = requests.post(url, data=json.dumps(payload), headers=headers)

print(response.text)
