# !/usr/bin/python3
# -- coding: utf-8 --
# -------------------------------
# 携程旅行签到脚本
# <AUTHOR>
# @Time 2025.01.18
# @Description
# ✨ 功能：
#     携程旅行APP签到获取积分
# ✨ 抓包步骤：
#     打开抓包工具
#     打开携程旅行APP
#     进入签到页面进行签到
#     找到 https://m.ctrip.com/restapi/soa2/22769/signToday 的请求
#     复制请求头中的 auth 参数值
# ✨ 变量示例：
#     export XIECHENG_TOKENS='D76A9DC1CA096D44F1A34BA85E0E2ADDD007F8E799125F155C7A216D1C665337'
#     多账号换行分割
# -------------------------------
# cron "30 9 * * *" script-path=xxx.py,tag=匹配cron用
# const $ = new Env('携程旅行签到')
# -------------------------------

from tools.common import BaseRun
import requests
import json
import os

class Run(BaseRun):
    def init_vars(self):
        """初始化变量和会话"""
        self.session = requests.Session()
        self.session.verify = False
        self.base_url = 'https://m.ctrip.com'
        self.sign_url = f'{self.base_url}/restapi/soa2/22769/signToday'

        # 请求头配置
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Linux; Android 15; 23013RK75C Build/AQ3A.240912.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/137.0.7151.88 Mobile Safari/537.36_CtripAPP_Android_8.81.6_eb64_Ctrip_CtripWireless_8.81.6_cDevice=23013RK75C_cSize=w1440*h3080__v=881.006_os=Android_osv=15_m=23013RK75C_brand=Redmi_vg=5_safeAreaTop=34_safeAreaBottom=0',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Content-Type': 'application/json',
            'sec-ch-ua-platform': '"Android"',
            'cookieorigin': 'https://m.ctrip.com',
            'sec-ch-ua': '"Android WebView";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?1',
            'origin': 'https://m.ctrip.com',
            'x-requested-with': 'ctrip.android.view',
            'sec-fetch-site': 'same-origin',
            'sec-fetch-mode': 'cors',
            'sec-fetch-dest': 'empty',
            'referer': 'https://m.ctrip.com/activitysetupapp/mkt/index/membersignin2021?isHideNavBar=YES&pushcode=mypoint&from_native_page=1&fromMemberTab=point',
            'accept-language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7'
        }

        # 签到结果信息
        self.continue_day = 0
        self.base_point = 0
        self.extra_point = 0
        self.extra_ctrip_point = 0
        self.tomorrow_point = 0

    def process_vars(self, info):
        """处理账号信息"""
        self.auth_token = info

        self.session.headers.update(self.headers)
        self.session.headers.update({
            'Cookie': f"cticket={self.auth_token}"
        })

    def process(self, info, _):
        """主要处理流程"""
        self.logger.info("===>🆕 变量准备")
        self.process_vars(info)

        self.logger.info("===>💥 开始签到")
        self.sign_in()

    def sign_in(self):
        """执行签到操作"""
        try:
            # 构建请求载荷
            payload = {
                "platform": "APP",
                "openId": "",
                "rmsToken": "",
                "head": {
                    "cid": "32001078290367030195",
                    "ctok": "",
                    "cver": "881.006",
                    "lang": "01",
                    "sid": "8913",
                    "syscode": "32",
                    "auth": self.auth_token,
                    "xsid": "",
                    "extension": []
                }
            }

            # 发送签到请求
            response = self.session.post(self.sign_url, data=json.dumps(payload))

            if response.status_code == 200:
                try:
                    res_json = response.json()
                    self.handle_sign_response(res_json)
                except json.JSONDecodeError:
                    self.logger.error(f'❌ 响应解析失败，原始响应: {response.text}')
            else:
                self.logger.error(f'❌ 签到请求失败，状态码: {response.status_code}')
                self.logger.error(f'❌ 响应内容: {response.text}')

        except Exception as e:
            self.logger.error(f'❌ 签到过程中发生异常: {str(e)}')

    def handle_sign_response(self, res_json):
        """处理签到响应"""
        try:
            code = res_json.get('code', -1)
            message = res_json.get('message', '未知错误')

            if code == 0:
                # 签到成功
                self.continue_day = res_json.get('continueDay', 0)
                self.base_point = res_json.get('baseIntegratedPoint', 0)
                self.extra_point = res_json.get('extraIntegratedPoint', 0)
                self.extra_ctrip_point = res_json.get('extraCtripPoint', 0)
                self.tomorrow_point = res_json.get('tomorrowIntegratedPoint', 0)

                self.logger.info(f'✅ 签到成功！')
                self.logger.info(f'📅 连续签到: {self.continue_day} 天')
                self.logger.info(f'🎯 获得积分: {self.base_point}')
                if self.extra_point > 0:
                    self.logger.info(f'🎁 额外积分: {self.extra_point}')
                if self.extra_ctrip_point > 0:
                    self.logger.info(f'🎁 额外携程积分: {self.extra_ctrip_point}')
                self.logger.info(f'🔮 明日可得积分: {self.tomorrow_point}')

                # 设置最终结果消息
                total_points = self.base_point + self.extra_point + self.extra_ctrip_point
                self.logger.final(f'签到成功！连续{self.continue_day}天，本次获得{total_points}积分')

            elif code == 400001:
                # 已经签到过了
                self.logger.info('ℹ️  今日已签到，无法重复签到')
                self.logger.final('今日已签到')

            else:
                # 其他错误
                self.logger.error(f'❌ 签到失败: {message} (错误码: {code})')

        except Exception as e:
            self.logger.error(f'❌ 处理签到响应时发生异常: {str(e)}')
            self.logger.error(f'❌ 原始响应: {res_json}')

if __name__ == '__main__':
    app_name = "携程旅行签到"
    app_env_name = "XIECHENG_TOKENS"
    local_script_name = os.path.basename(__file__)
    local_version = '2025.01.18'

    run = Run(app_name=app_name,
              app_env_name=app_env_name,
              script_version=local_version)
    run.main()
