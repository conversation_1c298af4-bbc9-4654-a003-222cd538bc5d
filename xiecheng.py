# !/usr/bin/python3
# -- coding: utf-8 --
# -------------------------------
# 携程旅行签到脚本
# <AUTHOR>
# @Time 2025.01.18
# @Description
# ✨ 功能：
#     携程旅行APP签到获取积分
# ✨ 抓包步骤：
#     打开抓包工具
#     打开携程旅行APP
#     进入签到页面进行签到
#     找到 https://m.ctrip.com/restapi/soa2/22769/signToday 的请求
#     复制请求头中的 auth 参数值
# ✨ 变量示例：
#     export XIECHENG_TOKENS='D76A9DC1xxxx'
#     多账号换行分割
# -------------------------------
# cron "01 10 * * *" script-path=xxx.py,tag=匹配cron用
# const $ = new Env('携程旅行签到')
# -------------------------------

from tools.common import BaseRun
import requests
import json
import os
import time

class Run(BaseRun):
    def init_vars(self):
        """初始化变量和会话"""
        self.session = requests.Session()
        self.session.verify = False
        self.base_url = 'https://m.ctrip.com'
        self.sign_url = f'{self.base_url}/restapi/soa2/22769/signToday'
        self.task_list_url = f'{self.base_url}/restapi/soa2/22598/userTaskList'
        self.todo_task_url = f'{self.base_url}/restapi/soa2/22598/todoTask'
        self.browse_done_url = f'{self.base_url}/restapi/soa2/22598/taskBrowseDone'
        self.award_task_url = f'{self.base_url}/restapi/soa2/22769/batchAwardTask'

        # 请求头配置
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Linux; Android 15; 23013RK75C Build/AQ3A.240912.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/137.0.7151.88 Mobile Safari/537.36_CtripAPP_Android_8.81.6_eb64_Ctrip_CtripWireless_8.81.6_cDevice=23013RK75C_cSize=w1440*h3080__v=881.006_os=Android_osv=15_m=23013RK75C_brand=Redmi_vg=5_safeAreaTop=34_safeAreaBottom=0',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Content-Type': 'application/json',
            'sec-ch-ua-platform': '"Android"',
            'cookieorigin': 'https://m.ctrip.com',
            'sec-ch-ua': '"Android WebView";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?1',
            'origin': 'https://m.ctrip.com',
            'x-requested-with': 'ctrip.android.view',
            'sec-fetch-site': 'same-origin',
            'sec-fetch-mode': 'cors',
            'sec-fetch-dest': 'empty',
            'referer': 'https://m.ctrip.com/activitysetupapp/mkt/index/membersignin2021?isHideNavBar=YES&pushcode=mypoint&from_native_page=1&fromMemberTab=point',
            'accept-language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7'
        }

        # 签到结果信息
        self.continue_day = 0
        self.base_point = 0
        self.extra_point = 0
        self.extra_ctrip_point = 0
        self.tomorrow_point = 0

        # 任务相关信息
        self.completed_tasks = []
        self.total_task_points = 0

    def process_vars(self, info):
        """处理账号信息"""
        self.auth_token = info

        self.session.headers.update(self.headers)
        self.session.headers.update({
            'Cookie': f"cticket={self.auth_token}"
        })

    def process(self, info, _):
        """主要处理流程"""
        self.logger.info("===>🆕 变量准备")
        self.process_vars(info)

        self.logger.info("===>💥 开始签到")
        self.sign_in()

        self.logger.info("===>📋 获取任务列表")
        self.do_tasks()

    def sign_in(self):
        """执行签到操作"""
        try:
            # 构建请求载荷
            payload = {
                "platform": "APP",
                "openId": "",
                "rmsToken": "",
                "head": {
                    "cid": "32001078290367030195",
                    "ctok": "",
                    "cver": "881.006",
                    "lang": "01",
                    "sid": "8913",
                    "syscode": "32",
                    "auth": self.auth_token,
                    "xsid": "",
                    "extension": []
                }
            }

            # 发送签到请求
            response = self.session.post(self.sign_url, data=json.dumps(payload))

            if response.status_code == 200:
                try:
                    res_json = response.json()
                    self.handle_sign_response(res_json)
                except json.JSONDecodeError:
                    self.logger.error(f'❌ 响应解析失败，原始响应: {response.text}')
            else:
                self.logger.error(f'❌ 签到请求失败，状态码: {response.status_code}')
                self.logger.error(f'❌ 响应内容: {response.text}')

        except Exception as e:
            self.logger.error(f'❌ 签到过程中发生异常: {str(e)}')

    def handle_sign_response(self, res_json):
        """处理签到响应"""
        try:
            code = res_json.get('code', -1)
            message = res_json.get('message', '未知错误')

            if code == 0:
                # 签到成功
                self.continue_day = res_json.get('continueDay', 0)
                self.base_point = res_json.get('baseIntegratedPoint', 0)
                self.extra_point = res_json.get('extraIntegratedPoint', 0)
                self.extra_ctrip_point = res_json.get('extraCtripPoint', 0)
                self.tomorrow_point = res_json.get('tomorrowIntegratedPoint', 0)

                self.logger.info(f'✅ 签到成功！')
                self.logger.info(f'📅 连续签到: {self.continue_day} 天')
                self.logger.info(f'🎯 获得积分: {self.base_point}')
                if self.extra_point > 0:
                    self.logger.info(f'🎁 额外积分: {self.extra_point}')
                if self.extra_ctrip_point > 0:
                    self.logger.info(f'🎁 额外携程积分: {self.extra_ctrip_point}')
                self.logger.info(f'🔮 明日可得积分: {self.tomorrow_point}')

                # 设置最终结果消息
                total_sign_points = self.base_point + self.extra_point + self.extra_ctrip_point
                total_all_points = total_sign_points + self.total_task_points

                if self.total_task_points > 0:
                    self.logger.final(f'签到成功！连续{self.continue_day}天，签到获得{total_sign_points}积分，任务获得{self.total_task_points}积分，总计{total_all_points}积分')
                else:
                    self.logger.final(f'签到成功！连续{self.continue_day}天，本次获得{total_sign_points}积分')

            elif code == 400001:
                # 已经签到过了
                self.logger.info('ℹ️  今日已签到，无法重复签到')
                self.logger.final('今日已签到')

            else:
                # 其他错误
                self.logger.error(f'❌ 签到失败: {message} (错误码: {code})')

        except Exception as e:
            self.logger.error(f'❌ 处理签到响应时发生异常: {str(e)}')
            self.logger.error(f'❌ 原始响应: {res_json}')

    def do_tasks(self):
        """执行任务流程"""
        try:
            # 1. 获取任务列表
            tasks = self.get_task_list()
            if not tasks:
                self.logger.info("ℹ️  没有可执行的任务")
                return

            # 2. 执行浏览任务
            browse_tasks = [task for task in tasks if task.get('eventType') == 'NO_REPEAT_BROWSE' and task.get('status') == 0]
            executed_task_ids = []

            if browse_tasks:
                self.logger.info(f"✅ 发现 {len(browse_tasks)} 个浏览任务")
                for task in browse_tasks:
                    if self.execute_browse_task(task):
                        executed_task_ids.append(task['id'])
                    time.sleep(2)  # 任务间隔
            else:
                self.logger.info("ℹ️  没有可执行的浏览任务")

            # 3. 领取刚刚执行完成的任务奖励
            if executed_task_ids:
                self.logger.info(f"✅ 开始领取 {len(executed_task_ids)} 个已执行任务的奖励")
                self.claim_task_rewards(executed_task_ids)
            else:
                self.logger.info("ℹ️  没有成功执行的任务需要领取奖励")

        except Exception as e:
            self.logger.error(f'❌ 执行任务过程中发生异常: {str(e)}')

    def get_task_list(self):
        """获取任务列表"""
        try:
            payload = {
                "channelCode": "2H3294O46M",
                "extMap": {
                    "mktTaskSort": ""
                },
                "oAuthHead": {},
                "platform": "APP",
                "rmsToken": "",
                "version": "1",
                "osType": "android",
                "appVersion": "8.81.6",
                "subOsType": "xiaomi",
                "_locale": "zh-CN",
                "head": {
                    "cid": "32001078290367030195",
                    "ctok": "",
                    "cver": "881.006",
                    "lang": "01",
                    "sid": "8913",
                    "syscode": "32",
                    "auth": self.auth_token,
                    "xsid": "",
                    "extension": []
                }
            }

            headers = self.headers.copy()
            headers.update({
                'cookieorigin': "https://contents.ctrip.com",
                'origin': "https://contents.ctrip.com",
                'sec-fetch-site': "same-site",
                'referer': "https://contents.ctrip.com/"
            })

            response = self.session.post(self.task_list_url, data=json.dumps(payload), headers=headers)

            if response.status_code == 200:
                res_json = response.json()
                todo_task_list = res_json.get('todoTaskList', [])
                self.logger.info(f"✅ 获取到 {len(todo_task_list)} 个任务")
                return todo_task_list
            else:
                self.logger.error(f'❌ 获取任务列表失败，状态码: {response.status_code}')
                return []

        except Exception as e:
            self.logger.error(f'❌ 获取任务列表异常: {str(e)}')
            return []

    def execute_browse_task(self, task):
        """执行浏览任务，返回是否执行成功"""
        try:
            task_id = task.get('id')
            task_name = task.get('displayName', '未知任务')
            browse_seconds = task.get('browseSeconds', 15)

            self.logger.info(f"==> 执行任务：{task_name} (ID: {task_id})")

            # 1. 开始任务
            todo_payload = {
                "channelCode": "2H3294O46M",
                "taskId": task_id,
                "status": 0,
                "done": 0,
                "oAuthHead": {},
                "platform": "APP",
                "rmsToken": "",
                "version": "1",
                "osType": "android",
                "appVersion": "8.81.6",
                "subOsType": "xiaomi",
                "_locale": "zh-CN",
                "allianceid": "5112619",
                "sid": "101281958",
                "ouid": "",
                "sourceid": "",
                "pushcode": "membersign",
                "innersid": "",
                "innerouid": "",
                "allianceInfo": {
                    "aid": "5112619",
                    "sid": "101281958",
                    "ouid": "",
                    "pushCode": "membersign",
                    "pageId": ""
                },
                "head": {
                    "cid": "32001078290367030195",
                    "ctok": "",
                    "cver": "881.006",
                    "lang": "01",
                    "sid": "8913",
                    "syscode": "32",
                    "auth": self.auth_token,
                    "xsid": "",
                    "extension": []
                }
            }

            headers = self.headers.copy()
            headers.update({
                'cookieorigin': "https://contents.ctrip.com",
                'origin': "https://contents.ctrip.com",
                'sec-fetch-site': "same-site",
                'referer': "https://contents.ctrip.com/",
                'priority': "u=1, i",
                'Cookie': f"_cticket={self.auth_token}"
            })

            response = self.session.post(self.todo_task_url, data=json.dumps(todo_payload), headers=headers)

            if response.status_code == 200:
                res_json = response.json()
                if res_json.get('code') == 200:
                    self.logger.info(f"✅ 任务开始成功，等待 {browse_seconds} 秒...")
                    time.sleep(browse_seconds)

                    # 2. 完成浏览任务
                    if self.complete_browse_task(task):
                        return True
                    else:
                        return False
                else:
                    self.logger.error(f"❌ 任务开始失败：{res_json.get('message', '未知错误')}")
                    return False
            else:
                self.logger.error(f"❌ 任务开始请求失败，状态码：{response.status_code}")
                return False

        except Exception as e:
            self.logger.error(f'❌ 执行浏览任务异常: {str(e)}')
            return False

    def complete_browse_task(self, task):
        """完成浏览任务，返回是否完成成功"""
        try:
            task_id = task.get('id')
            app_url = task.get('appUrl', '')
            event_condition = task.get('eventCondition', '{}')

            # 从eventCondition中提取_mktTaskActivityId
            import json as json_lib
            try:
                condition_data = json_lib.loads(event_condition)
                activity_id = condition_data.get('_mktTaskActivityId', f'task{task_id}')
            except:
                activity_id = f'task{task_id}'

            browse_payload = {
                "_taskDetailId": app_url,
                "_mktTaskActivityId": activity_id,
                "head": {
                    "auth": self.auth_token
                }
            }

            headers = self.headers.copy()
            headers.update({
                'cookieorigin': "https://m.ctrip.com",
                'origin': "https://m.ctrip.com",
                'sec-fetch-site': "same-origin",
                'referer': app_url if app_url else "https://m.ctrip.com/",
                'priority': "u=1, i",
                'Cookie': f"_cticket={self.auth_token}"
            })

            response = self.session.post(self.browse_done_url, data=json.dumps(browse_payload), headers=headers)

            if response.status_code == 200:
                res_json = response.json()
                code = res_json.get('code')
                message = res_json.get('message', '')

                if code == 200:
                    self.logger.info(f"✅ 浏览任务完成成功")
                    self.completed_tasks.append(task_id)
                    return True
                elif code == 401005:
                    self.logger.info(f"ℹ️  该项目已经浏览过了")
                    return False
                else:
                    self.logger.error(f"❌ 浏览任务完成失败：{message} (代码: {code})")
                    return False
            else:
                self.logger.error(f"❌ 浏览任务完成请求失败，状态码：{response.status_code}")
                return False

        except Exception as e:
            self.logger.error(f'❌ 完成浏览任务异常: {str(e)}')
            return False

    def claim_task_rewards(self, task_ids):
        """领取任务奖励"""
        try:
            if not task_ids:
                return

            award_payload = {
                "taskIdList": task_ids,
                "rmsToken": "fp=283247-CCED8C-319A47&vid=1731143856193.22ddFqDuhVxJ&pageId=10650086111&r=6e2e73f370cd4dfd904a158f15518140&ip=***************&rg=fin&screen=412x915&tz=+8&blang=en-US&oslang=en-US&ua=Mozilla%2F5.0%20(Linux%3B%20Android%2015%3B%2023013RK75C%20Build%2FAQ3A.240912.001%3B%20wv)%20AppleWebKit%2F537.36%20(KHTML%2C%20like%20Gecko)%20Version%2F4.0%20Chrome%2F137.0.7151.88%20Mobile%20Safari%2F537.36_CtripAPP_Android_8.81.6_eb64_Ctrip_CtripWireless_8.81.6_cDevice%3D23013RK75C_cSize%3Dw1440*h3080__v%3D881.006_os%3DAndroid_osv%3D15_m%3D23013RK75C_br&v=m17&bl=true&clientid=32001078290367030195&ftoken=",
                "platform": "APP",
                "head": {
                    "cid": "32001078290367030195",
                    "ctok": "",
                    "cver": "881.006",
                    "lang": "01",
                    "sid": "8913",
                    "syscode": "32",
                    "auth": self.auth_token,
                    "xsid": "",
                    "extension": []
                }
            }

            headers = self.headers.copy()
            headers.update({
                'cookieorigin': "https://contents.ctrip.com",
                'origin': "https://contents.ctrip.com",
                'sec-fetch-site': "same-site",
                'referer': "https://contents.ctrip.com/",
                'priority': "u=1, i",
                'Cookie': f"_cticket={self.auth_token}"
            })

            response = self.session.post(self.award_task_url, data=json.dumps(award_payload), headers=headers)

            if response.status_code == 200:
                res_json = response.json()
                code = res_json.get('code')
                message = res_json.get('message', '')

                if code == 0:
                    # 获得的积分
                    total_points = res_json.get("number")
                    self.total_task_points = total_points
                    self.logger.info(f"✅ 任务奖励领取成功，获得 {total_points} 积分")
                else:
                    self.logger.error(f"❌ 任务奖励领取失败：{message} (代码: {code})")
            else:
                self.logger.error(f"❌ 任务奖励领取请求失败，状态码：{response.status_code}")

        except Exception as e:
            self.logger.error(f'❌ 领取任务奖励异常: {str(e)}')

if __name__ == '__main__':
    app_name = "携程旅行签到"
    app_env_name = "XIECHENG_TOKENS"
    local_script_name = os.path.basename(__file__)
    local_version = '2025.06.25'

    run = Run(app_name=app_name,
              app_env_name=app_env_name,
              script_version=local_version)
    run.main()
