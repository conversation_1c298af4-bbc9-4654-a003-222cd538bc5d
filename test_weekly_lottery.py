#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试小米钱包天星借钱每周抽奖功能
"""

import sys
import os
import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from xiaomiqianbao import Run
import requests
from unittest.mock import Mock, patch

def test_is_monday():
    """测试周一检查功能"""
    print("=== 测试周一检查功能 ===")

    # 创建Run实例
    run = Run(app_name="小米钱包", app_env_name="XMQB_COOKIES", script_version="test")
    run.init_vars()

    # 测试当前日期
    current_result = run.is_monday()
    current_day = datetime.datetime.now().strftime("%A")
    print(f"今天是: {current_day}")
    print(f"是否为周一: {current_result}")
    print(f"预期结果: {current_day == 'Monday'}")
    print(f"测试结果: {'✅ 通过' if current_result == (current_day == 'Monday') else '❌ 失败'}")
    print()

def test_get_weekly_lottery_tasks():
    """测试获取抽奖任务列表"""
    print("=== 测试获取抽奖任务列表 ===")

    # 创建Run实例
    run = Run(app_name="小米钱包", app_env_name="XMQB_COOKIES", script_version="test")
    run.init_vars()

    # 模拟session
    mock_session = Mock(spec=requests.Session)
    mock_cookies = Mock()
    mock_cookies.get.return_value = "test_user_id"
    mock_session.cookies = mock_cookies

    # 模拟API响应
    mock_response = Mock()
    mock_response.status_code = 200
    mock_response.json.return_value = {
        "success": True,
        "data": {
            "activity": {"code": "week_143_20250609"},
            "taskInfo": {
                "tasks": [
                    {
                        "taskId": 76792843,
                        "status": 0,
                        "title": "薅羊毛区%%无门槛%%",
                        "subTitle": "完成周周赢豪礼签到",
                        "prizeDTO": {
                            "prizeId": 1117,
                            "name": "小米小爱音箱Play 增强版"
                        }
                    },
                    {
                        "taskId": 76792844,
                        "status": 0,
                        "title": "填写问卷%%性价比%%",
                        "subTitle": "反馈天星借钱使用体验",
                        "prizeDTO": {
                            "prizeId": 1116,
                            "name": "REDMI Buds 6 Pro"
                        }
                    }
                ]
            }
        }
    }
    mock_session.post.return_value = mock_response

    # 测试方法
    result = run.jrairstar_get_weekly_lottery_tasks(mock_session)

    print(f"返回任务数量: {len(result)}")
    print(f"预期任务数量: 1 (只有包含'无门槛'的任务)")

    if result:
        task = result[0]
        print(f"任务详情: {task}")
        print(f"任务标题: {task.get('title')}")
        print(f"奖品名称: {task.get('prizeName')}")

    print(f"测试结果: {'✅ 通过' if len(result) == 1 and '无门槛' in result[0]['title'] else '❌ 失败'}")
    print()

def test_fetch_lottery_chance():
    """测试获取抽奖机会"""
    print("=== 测试获取抽奖机会 ===")

    # 创建Run实例
    run = Run(app_name="小米钱包", app_env_name="XMQB_COOKIES", script_version="test")
    run.init_vars()

    # 模拟session
    mock_session = Mock(spec=requests.Session)
    mock_cookies = Mock()
    mock_cookies.get.return_value = "test_user_id"
    mock_session.cookies = mock_cookies

    # 模拟API响应
    mock_response = Mock()
    mock_response.status_code = 200
    mock_response.json.return_value = {
        "success": True,
        "data": {"chanceCount": 1}
    }
    mock_session.post.return_value = mock_response

    # 测试数据
    task_data = {
        "taskId": 76792843,
        "activityCode": "week_143_20250609"
    }

    # 测试方法
    result = run.jrairstar_fetch_lottery_chance(mock_session, task_data)

    print(f"返回抽奖机会数: {result}")
    print(f"预期抽奖机会数: 1")
    print(f"测试结果: {'✅ 通过' if result == 1 else '❌ 失败'}")
    print()

def test_apply_lottery():
    """测试抽奖报名"""
    print("=== 测试抽奖报名 ===")

    # 创建Run实例
    run = Run(app_name="小米钱包", app_env_name="XMQB_COOKIES", script_version="test")
    run.init_vars()

    # 模拟session
    mock_session = Mock(spec=requests.Session)
    mock_cookies = Mock()
    mock_cookies.get.return_value = "test_user_id"
    mock_session.cookies = mock_cookies

    # 模拟API响应
    mock_response = Mock()
    mock_response.status_code = 200
    mock_response.json.return_value = {
        "success": True,
        "name": "Success"
    }
    mock_session.post.return_value = mock_response

    # 测试数据
    task_data = {
        "taskId": 76792843,
        "prizeId": 1117,
        "prizeName": "小米小爱音箱Play 增强版"
    }

    # 测试方法
    result = run.jrairstar_apply_lottery(mock_session, task_data)

    print(f"抽奖报名结果: {result}")
    print(f"预期结果: True")
    print(f"测试结果: {'✅ 通过' if result == True else '❌ 失败'}")
    print()

if __name__ == "__main__":
    print("开始测试小米钱包天星借钱每周抽奖功能...")
    print()

    test_is_monday()
    test_get_weekly_lottery_tasks()
    test_fetch_lottery_chance()
    test_apply_lottery()

    print("=== 测试完成 ===")
    print()
    print("如需测试完整功能，请设置环境变量并运行:")
    print("export XMQB_COOKIES='你的Cookie值'")
    print("python xiaomiqianbao.py")
