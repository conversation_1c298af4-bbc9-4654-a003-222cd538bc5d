# !/usr/bin/python3
# -- coding: utf-8 --
# -------------------------------
# 塔斯汀小程序
# <AUTHOR>
# @Time 2025.06.12
# @Description
# ✨ 功能：
#     塔斯汀小程序签到获取积分
# ✨ 抓包步骤：
#     打开抓包工具
#     打开塔斯汀小程序
#     授权登陆
#     找到签到请求，获取请求体中的 memberPhone，获取请求头中的 user-token
#     格式：memberPhone;user-token
# ✨ 变量示例：
#     export TASITING_COOKIES='18279xxxx;ssxxx'，多账号换行分割
# -------------------------------
# cron "12 9 * * *" script-path=xxx.py,tag=匹配cron用
# const $ = new Env('塔斯汀小程序')
# -------------------------------

from tools.common import BaseRun
import requests
import json
import os

class Run(BaseRun):
    def init_vars(self):
        self.session = requests.Session()
        self.session.verify = False
        self.base_url = 'https://sss-web.tastientech.com'
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Linux; Android 15; 23013RK75C Build/AQ3A.240912.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/134.0.6998.136 Mobile Safari/537.36 XWEB/1340123 MMWEBSDK/20250201 MMWEBID/8758 MicroMessenger/8.0.58.2841(0x28003A35) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64 MiniProgramEnv/android',
            'Content-Type': 'application/json',
            'channel': '1',
            'version': '3.21.0',
            'charset': 'utf-8',
            'Referer': 'https://servicewechat.com/wx557473f23153a429/427/page-frame.html'
        }
        # 用户信息
        self.member_phone = ''
        self.user_token = ''
        # 积分信息
        self.before_point = 0
        self.after_point = 0
        self.gained_point = 0
        # 签到结果
        self.sign_result = ''
        self.reward_info = []

    def process_vars(self, info):
        cookies = info.split(';')
        if len(cookies) >= 2:
            self.member_phone = cookies[0].strip()
            self.user_token = cookies[1].strip()
            self.headers['user-token'] = self.user_token
            self.session.headers.update(self.headers)
        else:
            self.logger.error('❌ COOKIE格式错误，应为：memberPhone;user-token')

    def process(self, info, _):
        self.logger.info("===>🆕 变量准备")
        self.process_vars(info)
        if not self.member_phone or not self.user_token:
            self.logger.error('❌ 缺少必要的认证信息')
            return

        self.logger.info(f"===>🔛 用户 {self.member_phone}")

        # 获取活动ID
        self.logger.info("===>🔍 获取签到活动ID")
        if not self.get_activity_id():
            self.logger.error('❌ 获取活动ID失败，无法继续执行')
            return

        # 获取签到前积分
        self.logger.info("===>📊 获取签到前积分")
        self.get_current_point(is_before=True)

        # 执行签到
        self.logger.info("===>💥 开始签到")
        self.signin()

        # 获取签到后积分（仅在签到成功时）
        if self.sign_result.startswith('✅'):
            self.logger.info("===>📊 获取签到后积分")
            self.get_current_point(is_before=False)
            self.gained_point = self.after_point - self.before_point

        # 获取后续奖励列表
        self.logger.info("===>🎁 获取后续奖励列表")
        self.get_future_rewards()

        # 输出最终结果
        self.show_final_result()

    def signin(self):
        url = f"{self.base_url}/api/sign/member/signV2"
        payload = {
            "activityId": self.activity_id,
            "memberName": "",
            "memberPhone": self.member_phone
        }

        try:
            response = self.session.post(url, data=json.dumps(payload))
            if response.status_code == 200:
                res_json = response.json()
                code = res_json.get('code', None)
                message = res_json.get('msg', '')

                if code == 200:
                    result = res_json.get('result', {})
                    continuous_num = result.get('continuousNum', 0)
                    reward_info_list = result.get('rewardInfoList', [])

                    # 解析奖励信息
                    rewards = []
                    for reward in reward_info_list:
                        if reward.get('rewardType') == 5:  # 积分奖励
                            point = reward.get('point', 0)
                            rewards.append(f"{point}积分")
                        elif reward.get('rewardType') == 1:  # 优惠券奖励
                            reward_name = reward.get('rewardName', '优惠券')
                            rewards.append(reward_name)

                    reward_text = "、".join(rewards) if rewards else "无奖励"
                    self.sign_result = f"✅ 签到成功，连续签到{continuous_num}天，获得：{reward_text}"
                    self.reward_info = reward_info_list
                    self.logger.info(self.sign_result)

                    # 显示详细的奖励信息
                    if reward_info_list:
                        self.show_received_rewards(reward_info_list)
                elif code == 500:
                    self.sign_result = f"ℹ️  {message}"
                    self.logger.info(self.sign_result)
                else:
                    self.sign_result = f"❌ 签到失败：{message}"
                    self.logger.error(self.sign_result)
            else:
                self.sign_result = f"❌ 签到请求失败，状态码：{response.status_code}"
                self.logger.error(self.sign_result)
                self.logger.error(f"❌ 响应内容：{response.text}")
        except Exception as e:
            self.sign_result = f"❌ 签到异常：{str(e)}"
            self.logger.error(self.sign_result)

    def get_activity_id(self):
        """获取签到活动ID"""
        url = f"{self.base_url}/api/minic/shop/intelligence/banner/c/list"
        try:
            response = self.session.post(url, data=json.dumps({}))
            if response.status_code == 200:
                res_json = response.json()
                if res_json.get('code') == 200:
                    result = res_json.get('result', [])

                    # 查找包含"签到"关键字的banner
                    sign_banner = None
                    for banner in result:
                        banner_name = banner.get('bannerName', '')
                        if '签到' in banner_name:
                            sign_banner = banner
                            break

                    if not sign_banner:
                        self.logger.error('❌ 未找到签到活动banner')
                        # 调试：显示所有banner名称
                        banner_names = [banner.get('bannerName', '') for banner in result]
                        self.logger.error(f'❌ 可用的banner列表：{banner_names}')
                        return False

                    # 解析jumpPara字段
                    jump_para = sign_banner.get('jumpPara', '')
                    if not jump_para:
                        self.logger.error('❌ jumpPara字段为空')
                        # 调试：显示banner的所有字段
                        self.logger.error(f'❌ 签到banner内容：{sign_banner}')
                        return False

                    try:
                        # 第一次JSON解析
                        jump_para_obj = json.loads(jump_para)

                        # 检查是否直接包含activityId
                        activity_id = jump_para_obj.get('activityId')
                        if activity_id:
                            self.activity_id = activity_id
                            self.logger.info(f"🔍 获取到签到活动ID：{activity_id}")
                            return True

                        # 如果没有直接的activityId，尝试解析path字段
                        path = jump_para_obj.get('path', '')
                        if not path:
                            self.logger.error('❌ jumpPara中既没有activityId也没有path字段')
                            self.logger.error(f'❌ jumpPara解析结果：{jump_para_obj}')
                            return False

                        # URL解码
                        from urllib.parse import unquote, urlparse, parse_qs
                        decoded_path = unquote(path)

                        # 解析URL参数
                        parsed_url = urlparse(decoded_path)
                        query_params = parse_qs(parsed_url.query)

                        encoded_jump_para = query_params.get('jumpPara', [None])[0]
                        if not encoded_jump_para:
                            self.logger.error('❌ URL参数中未找到jumpPara')
                            return False

                        # 解码并解析最终的jumpPara
                        decoded_jump_para = unquote(encoded_jump_para)
                        final_jump_para_obj = json.loads(decoded_jump_para)

                        activity_id = final_jump_para_obj.get('activityId')
                        if not activity_id:
                            self.logger.error('❌ 最终jumpPara中未找到activityId')
                            return False

                        self.activity_id = activity_id
                        self.logger.info(f"🔍 获取到签到活动ID：{activity_id}")
                        return True

                    except json.JSONDecodeError as e:
                        self.logger.error(f'❌ JSON解析失败：{str(e)}')
                        return False
                    except Exception as e:
                        self.logger.error(f'❌ 解析jumpPara异常：{str(e)}')
                        return False

                else:
                    self.logger.error(f"❌ 获取banner列表失败：{res_json.get('msg', '未知错误')}")
                    return False
            else:
                self.logger.error(f"❌ 获取banner列表请求失败，状态码：{response.status_code}")
                return False
        except Exception as e:
            self.logger.error(f"❌ 获取活动ID异常：{str(e)}")
            return False

    def get_current_point(self, is_before=True):
        """获取当前积分
        Args:
            is_before: True表示签到前，False表示签到后
        """
        url = f"{self.base_url}/api/wx/point/myPoint"
        try:
            response = self.session.post(url, data=json.dumps({}))
            if response.status_code == 200:
                res_json = response.json()
                if res_json.get('code') == 200:
                    result = res_json.get('result', {})
                    current_point = result.get('point', 0)
                    if is_before:
                        self.before_point = current_point
                        self.logger.info(f"📊 签到前积分：{current_point}")
                    else:
                        self.after_point = current_point
                        self.logger.info(f"📊 签到后积分：{current_point}")
                else:
                    self.logger.error(f"❌ 获取积分失败：{res_json.get('msg', '未知错误')}")
            else:
                self.logger.error(f"❌ 获取积分请求失败，状态码：{response.status_code}")
        except Exception as e:
            self.logger.error(f"❌ 获取积分异常：{str(e)}")

    def get_future_rewards(self):
        """获取后续奖励列表"""
        url = f"{self.base_url}/api/sign/member/signInfoV2"
        payload = {"activityId": self.activity_id}
        try:
            response = self.session.post(url, data=json.dumps(payload))
            if response.status_code == 200:
                res_json = response.json()
                if res_json.get('code') == 200:
                    result = res_json.get('result', {})
                    activity_info = result.get('activityInfo', {})
                    reward_list = activity_info.get('rewardList', [])

                    if reward_list:
                        self.logger.info("🎁 后续签到奖励预览：")
                        for reward in reward_list[:7]:  # 显示接下来7天的奖励
                            day_num = reward.get('dayNum', 0)
                            reward_name = reward.get('rewardName', '')
                            reward_type = reward.get('rewardType', 0)

                            if reward_type == 1:  # 优惠券
                                coupon_info = reward.get('couponInfo', [])
                                if coupon_info:
                                    coupon_content = coupon_info[0].get('couponContent', '')
                                    coupon_time = coupon_info[0].get('couponTime', '')
                                    self.logger.info(f"   第{day_num}天：{reward_name} ({coupon_content}，{coupon_time})")
                                else:
                                    self.logger.info(f"   第{day_num}天：{reward_name}")
                            elif reward_type == 2:  # 积分
                                point = reward.get('point', 0)
                                self.logger.info(f"   第{day_num}天：{point}积分")
                            else:
                                self.logger.info(f"   第{day_num}天：{reward_name}")
                    else:
                        self.logger.info("🎁 暂无后续奖励信息")
                else:
                    self.logger.error(f"❌ 获取奖励列表失败：{res_json.get('msg', '未知错误')}")
            else:
                self.logger.error(f"❌ 获取奖励列表请求失败，状态码：{response.status_code}")
        except Exception as e:
            self.logger.error(f"❌ 获取奖励列表异常：{str(e)}")

    def show_received_rewards(self, reward_info_list):
        """显示已获得的奖励详情"""
        self.logger.info("🎁 本次签到获得的奖励详情：")
        for i, reward in enumerate(reward_info_list, 1):
            reward_type = reward.get('rewardType', 0)

            if reward_type == 5:  # 积分奖励
                point = reward.get('point', 0)
                self.logger.info(f"   奖励{i}：{point}积分")

            elif reward_type == 1:  # 优惠券奖励
                reward_name = reward.get('rewardName', '优惠券')
                coupon_info = reward.get('couponInfo', [])
                if coupon_info:
                    coupon = coupon_info[0]
                    coupon_content = coupon.get('couponContent', '')
                    coupon_time = coupon.get('couponTime', '')
                    coupon_name = coupon.get('name', reward_name)
                    self.logger.info(f"   奖励{i}：{coupon_name}")
                    self.logger.info(f"        优惠内容：{coupon_content}")
                    self.logger.info(f"        有效期：{coupon_time}")
                else:
                    self.logger.info(f"   奖励{i}：{reward_name}")

            elif reward_type == 2:  # 其他积分类型
                point = reward.get('point', 0)
                reward_name = reward.get('rewardName', f'{point}积分')
                self.logger.info(f"   奖励{i}：{reward_name}")

            else:  # 其他类型奖励
                reward_name = reward.get('rewardName', '未知奖励')
                self.logger.info(f"   奖励{i}：{reward_name}")

    def show_final_result(self):
        """显示最终结果"""
        self.logger.final(f"签到结果：{self.sign_result}")

        # 如果签到成功且有积分变化，显示积分变化情况
        if self.sign_result.startswith('✅') and self.after_point > 0:
            if self.gained_point > 0:
                self.logger.final(f"积分变化：{self.before_point} → {self.after_point} (+{self.gained_point})")
            elif self.gained_point == 0:
                self.logger.final(f"积分无变化：{self.before_point}")
            else:
                self.logger.final(f"积分变化：{self.before_point} → {self.after_point} ({self.gained_point})")

if __name__ == '__main__':
    app_name = "塔斯汀小程序"
    app_env_name = "TASITING_COOKIES"
    local_script_name = os.path.basename(__file__)
    local_version = '2025.06.12'
    run = Run(app_name=app_name,
              app_env_name=app_env_name,
              script_version=local_version)
    run.main()
