# !/usr/bin/python3
# -- coding: utf-8 --
# -------------------------------
# 中国移动云盘
# <AUTHOR> https://github.com/smallfawn/QLScriptPublic/blob/main/yidongyunpan.py
# @Modify qianfanguojin
# @Time 2024.09.16
# @Description
# ✨ 功能：
#     中国移动云盘签到 基础任务 果园 云朵大作战
# ✨ 抓包步骤：
#     打开抓包工具
#     打开 中国移动云盘 APP，以下两种方式取其中一种获取 Authorization 值和 Token 值
#       - [简易方法，开抓包进App，搜refresh，找到authTokenRefresh.do ，请求头中的Authorization，响应体<token> xxx</token> 中xxx值（新版加密抓这个）]
#       - [20241010更新，开抓包进App，搜refresh，找到authTokenRefresh.do ，请求头中的APP_AUTH，响应头中 NOTE_TOKEN值（新版加密抓这个）]
#       组装成 Authorization值@手机号@token值
# ✨ 设置青龙变量：
#     export ZGYDYP_CREDENTIALS='Basic bwxxx;133xxx;ksxxx'参数值，多账号换行分割
# -------------------------------
# cron "16 16 8 * * *" script-path=xxx.py,tag=匹配cron用
# const $ = new Env('中国移动云盘');
# -------------------------------
from tools.common import BaseRun
#from typing import override
import os
import random
import time
import requests
import json



class Run(BaseRun):
    #@override
    def init_vars(self):
        self.session = requests.Session()
        self.common_url = 'https://caiyun.feixin.10086.cn'
        self.new_common_url = "https://m.mcloud.139.com"
        self.user_agent = 'Mozilla/5.0 (Linux; Android 11; M2012K10C Build/RP1A.200720.011; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/90.0.4430.210 Mobile Safari/537.36 MCloudApp/10.0.1'
        self.jwt_headers = {
            'User-Agent': 'Mozilla/5.0 (Linux; Android 11; M2012K10C Build/RP1A.200720.011; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/90.0.4430.210 Mobile Safari/537.36 MCloudApp/10.0.1',
            'Accept': '*/*',
        }
        self.notebook_id = None
        self.note_token = None
        self.note_auth = None
        self.click_num = 30  # 定义抽奖次数和摇一摇戳一戳次数
        self.draw = 1  # 抽奖次数，首次免费
        self.before_clouds = 0
        self.after_clouds = 0
        self.wait_receive_clouds = 0
        self.wait_receive_rewards = ""
    #@override
    def process_vars(self, info):
        self.authorization,self.account,self.auth_token = info.split(";")
        self.timestamp = str(int(round(time.time() * 1000)))
        self.encrypt_account = self.account[:3] + "*" * 4 + self.account[7:]
        self.cookies = {'sensors_stay_time': self.timestamp}
        #self.session.cookies.update(self.cookies)
    #@override
    def process(self, info, _):
        self.process_vars(info)
        self.logger.info('===>🔛 刷新登录令牌')
        if self.jwt():
            self.get_personal_info()
            self.logger.info('===>💥 签到')
            self.signin()
            self.logger.info('===>🎁 点击任务')
            self.click()
            self.logger.info('===>📌 云盘任务')
            self.do_tasklist(url = 'sign_in_3', app_type = 'cloud_app')
            self.logger.info('===>📧 139邮箱任务')
            self.do_tasklist(url = 'newsign_139mail', app_type = 'email_app')
            self.logger.info('===>☁️  云朵大作战')
            self.cloud_game()
            self.logger.info('===>📰 公众号签到')
            self.wxsign()
            self.logger.info('===>🕹  摇一摇抽奖')
            self.shake()
            self.surplus_num()
            self.logger.info('===>💼 自动备份奖励')
            self.backup_cloud()
            self.logger.info('===>🔔 开启通知奖励')
            self.open_send()
            self.logger.info('===>🏆 查询待领取奖励')
            self.receive()
            self.get_personal_info(end=True)
            msg = ""
            msg += f'待领取云朵: {self.wait_receive_clouds} 云朵\n'
            msg += f'待领取奖品:\n{self.wait_receive_rewards}'
            self.logger.final(f"总云朵变化：{self.before_clouds} --> {self.after_clouds}\n{msg}")
    # 捕获异常
    def catch_errors(func):
        def wrapper(self, *args, **kwargs):
            try:
                return func(self, *args, **kwargs)
            except Exception as e:
                print("错误:",  f"{e.with_traceback()}\n")
                 # 错误信息
            return None

        return wrapper

    # 随机延迟默认1-1.5s
    def sleep(self, min_delay=1, max_delay=1.5):
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)

    # 刷新令牌
    def sso(self):
        sso_url = 'https://yun.139.com/orchestration/auth-rebuild/token/v1.0/querySpecToken'
        sso_headers = {
            'Authorization': self.authorization,
            'User-Agent': "Mozilla/5.0 (Linux; Android 15; 23013RK75C Build/AQ3A.240912.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/131.0.6778.201 Mobile Safari/537.36 MCloudApp/11.4.0",
            'Content-Type': 'application/json',
            'Accept': '*/*',
        }
        sso_payload = {"account": self.account, "toSourceId": "001003"}
        sso_data = requests.post(sso_url, headers = sso_headers, json = sso_payload).json()
        if sso_data['success']:
            refresh_token = sso_data['data']['token']
            self.logger.info('✅ 刷新 sso 令牌成功')
            return refresh_token
        else:
            self.logger.error('❌ 刷新 sso 令牌失败')
            return None

    # jwt 令牌
    def jwt(self):
        # 获取jwttoken
        token = self.sso()
        if token is not None:
            jwt_url = f"{self.new_common_url}/portal/auth/tyrzLogin.action?ssoToken={token}"
            jwt_data = self.session.post(jwt_url).json()
            if jwt_data['code'] != 0:
                self.logger.error(f"❌ 刷新 jwt 令牌失败: {jwt_data['msg']}")
                return False
            self.jwt_headers['jwttoken'] = jwt_data['result']['token']
            self.cookies['jwtToken'] = jwt_data['result']['token']
            self.session.headers.update(self.jwt_headers)
            self.session.cookies.update(self.cookies)
            self.logger.info("✅ 刷新 jwt 令牌成功")
            return True
        else:
            self.logger.error("❌ 刷新 jwt 令牌失败: cookie 或变量可能失效了")
            return False

    #签到
    @catch_errors
    def signin(self):
        self.sleep()
        signin_url = f'{self.common_url}/market/manager/commonMarketconfig/getByMarketRuleName?marketName=sign_in_3'
        signin_data = self.session.get(signin_url).json()
        if self.singined:
            self.logger.info('ℹ️  已经签到过了')
        else:
            signin_url = f'{self.common_url}/market/manager/commonMarketconfig/getByMarketRuleName?marketName=sign_in_3'
            signin_data = self.session.get(signin_url, headers = self.jwt_headers,
                                            cookies = self.cookies).json()
            if signin_data['msg'] == 'success':
                self.logger.info('✅ 签到成功')
            else:
                self.logger.error(signin_data['msg'])
                #self.log_info(signin_data['msg'])
        # 签到翻倍云朵领取
        signin_multiple_url = f'{self.new_common_url}/market/signin/page/multiple'
        signin_multiple_data = self.session.get(signin_multiple_url).json()
        if signin_multiple_data['code'] == 500:
            if "已领取" in signin_multiple_data['msg']:
                self.logger.info('ℹ️  本月翻倍云朵已领取')
            else:
                self.logger.info(signin_multiple_data['msg'])
        elif signin_multiple_data['code'] == 0:
            if signin_multiple_data['result']['cloudCount']:
                self.logger.info(f"✅ 翻倍云朵领取成功, 翻倍得到云朵 {signin_multiple_data['result']['cloudCount']}")
            else:
                self.logger.info(signin_multiple_data['msg'])
        else:
            self.logger.error(signin_multiple_data['msg'])


    # 用户信息查询
    @catch_errors
    def get_personal_info(self, end=False):
        self.sleep()
        self.singined = False
        info_url = f'{self.common_url}/market/signin/page/info?client=app'
        info_data = self.session.get(info_url).json()
        if info_data['msg'] == 'success':
            today_sign_in = info_data['result'].get('todaySignIn', True)
            total = info_data['result'].get('total', 0)
            if today_sign_in:
                self.singined = True
            if not end:
                # self(f'运行前云朵数量: 【{total}】')
                self.before_clouds = total
            else:
                self.after_clouds = total
            # Log(f'运行后云朵数量: 【{total}】')
        else:
            self.logger.error(info_data['msg'])
            #self.log_info(info_data['msg'])

    # 戳一下
    def click(self):
        self.sleep()
        url = f"{self.common_url}/market/signin/task/click?key=task&id=319"
        successful_click = 0  # 获得次数
        for i in range(self.click_num):
            return_data = self.session.get(url, headers = self.jwt_headers, cookies= self.cookies ).json()
            time.sleep(0.2)
            if 'result' in return_data:
                self.logger.info(f'✅ 执行第 {i+1} 次获得奖励: {return_data["result"]}')
                successful_click += 1
        if successful_click == 0:
            self.logger.info(f'ℹ️  未获得奖励 x {self.click_num}')

    # 刷新笔记token
    @catch_errors
    def refresh_notetoken(self):
        note_url = 'http://mnote.caiyun.feixin.10086.cn/noteServer/api/authTokenRefresh.do'
        note_payload = {
            "authToken": self.auth_token,
            "userPhone": self.account
        }
        note_headers = {
            'X-Tingyun-Id': 'p35OnrDoP8k;c=2;r=**********;u=43ee994e8c3a6057970124db00b2442c::8B3D3F05462B6E4C',
            'Charset': 'UTF-8',
            'Connection': 'Keep-Alive',
            'User-Agent': 'mobile',
            'APP_CP': 'android',
            'CP_VERSION': '3.2.0',
            'x-huawei-channelsrc': '********',
            'Host': 'mnote.caiyun.feixin.10086.cn',
            'Content-Type': 'application/json; charset=UTF-8',
            'Accept-Encoding': 'gzip'
        }

        try:
            response = requests.post(note_url, headers = note_headers, data = note_payload)
            response.raise_for_status()
        except requests.exceptions.RequestException as e:
            self.logger.error(f'❌ 出错了: {e}')
            return
        self.note_token = response.headers.get('NOTE_TOKEN')
        self.note_auth = response.headers.get('APP_AUTH')

    # 任务列表
    def do_tasklist(self, url, app_type):
        url = f'{self.common_url}/market/signin/task/taskList?marketname={url}'
        return_data = self.session.get(url).json()
        self.sleep()
        # 任务列表
        task_list = return_data.get('result', {})
        try:
            for task_type, tasks in task_list.items():
                if task_type in ["new", "hidden", "hiddenabc"]:
                    continue
                if app_type == 'cloud_app':
                    if task_type == "month":
                        self.logger.info('==>📆 云盘每月任务')
                        for month in tasks:
                            task_id = month.get('id')
                            task_name = month.get('name', '')
                            task_status = month.get('state', '')
                            if task_status == 'FINISH':
                                self.logger.info(f'✅ 已完成: {task_name}')
                                continue
                            if task_id in [426]:
                                continue
                            if task_id in [110, 472, 426, 113]:
                                self.logger.info(f'ℹ️  需手动: {month.get("name")}')
                                continue
                            self.logger.info(f'去完成: {task_name}')
                            self.do_task(task_id, task_type = 'month', app_type = 'cloud_app')
                            time.sleep(2)
                    elif task_type == "day":
                        self.logger.info('==>📆 云盘每日任务')
                        for day in tasks:
                            task_id = day.get('id')
                            if task_id == 404:
                                continue
                            task_name = day.get('name')
                            task_status = day.get('state', '')
                            if task_status == 'FINISH':
                                self.logger.info(f'✅ 已完成: {task_name}')
                                continue
                            self.logger.info(f'去完成: {task_name}')
                            self.do_task(task_id, task_type = 'day', app_type = 'cloud_app')
                    elif task_type == "time":
                        self.logger.info('==>🔥 云盘热门任务')
                        for time_task in tasks:
                            task_id = time_task.get('id')
                            task_name = time_task.get('name', '')
                            task_status = time_task.get('state', '')
                            if task_status == 'FINISH':
                                self.logger.info(f'✅ 已完成: {task_name}')
                                continue
                            if task_id in [120, 385, 406, 480, 503, 483]:
                                continue
                            else:
                                self.logger.info(f'ℹ️  需手动: {time_task.get("name")}')
                elif app_type == 'email_app':
                    if task_type == "month":
                        self.logger.info('==>📆 139邮箱每月任务')
                        for month in tasks:
                            task_id = month.get('id')
                            task_name = month.get('name', '')
                            task_status = month.get('state', '')
                            if task_status == 'FINISH':
                                self.logger.info(f'✅ 已完成: {task_name}')
                                continue
                            if task_id in [1021, 1008]:
                                continue
                            else:
                                self.logger.info(f'ℹ️  需手动: {month.get("name")}')
                                continue
                    elif task_type == "time":
                        self.logger.info('==>🔥 139邮箱热门任务')
                        for time_task in tasks:
                            task_id = time_task.get('id')
                            task_name = time_task.get('name', '')
                            task_status = time_task.get('state', '')
                            if task_status == 'FINISH':
                                self.logger.info(f'✅ 已完成: {task_name}')
                                continue
                            if task_id in [1022, 1018]:
                                continue
                            else:
                                self.logger.info(f'ℹ️  需手动: {time_task.get("name")}')
        except Exception as e:
            self.logger.error(f'❌ 错误信息:{e}')

    # 做任务
    @catch_errors
    def do_task(self, task_id, task_type, app_type):
        self.sleep()
        task_url = f'{self.common_url}/market/signin/task/click?key=task&id={task_id}'
        if task_id == 409:
            task_url = f'{self.new_common_url}/market/signin/task/click?key=task2&id={task_id}'
        elif app_type == 'email_app':
            task_url = f'{self.common_url}:7071/market/signin/task/click?key=task&id={task_id}'
        self.session.get(task_url)
        if app_type == 'cloud_app':
            if task_type == 'day':
                if task_id == 106:
                    self.logger.info('开始上传文件，默认 0 kb')
                    self.updata_file()
                elif task_id == 107:
                    self.refresh_notetoken()
                    self.logger.info('获取默认笔记 id')
                    note_url = 'http://mnote.caiyun.feixin.10086.cn/noteServer/api/syncNotebookV3.do'
                    headers = {
                        'X-Tingyun-Id': 'p35OnrDoP8k;c=2;r=**********;u=43ee994e8c3a6057970124db00b2442c::8B3D3F05462B6E4C',
                        'Charset': 'UTF-8',
                        'Connection': 'Keep-Alive',
                        'User-Agent': 'mobile',
                        'APP_CP': 'android',
                        'CP_VERSION': '3.2.0',
                        'x-huawei-channelsrc': '********',
                        'APP_NUMBER': self.account,
                        'APP_AUTH': self.note_auth,
                        'NOTE_TOKEN': self.note_token,
                        'Host': 'mnote.caiyun.feixin.10086.cn',
                        'Content-Type': 'application/json; charset=UTF-8',
                        'Accept': '*/*'
                    }
                    payload = {
                        "addNotebooks": [],
                        "delNotebooks": [],
                        "notebookRefs": [],
                        "updateNotebooks": []
                    }
                    return_data = requests.post(url = note_url, headers = headers, data = payload).json()
                    if return_data is None:
                        self.logger.error("❌ 获取默认笔记 id 出错了")
                        return
                    self.notebook_id = return_data['notebooks'][0]['notebookId']
                    self.logger.info('开始创建笔记')
                    self.create_note(headers)
            elif task_type == 'month':
                pass
        elif app_type == 'email_app':
            if task_type == 'month':
                pass

    # 上传文件任务
    @catch_errors
    def updata_file(self):
        url = 'http://ose.caiyun.feixin.10086.cn/richlifeApp/devapp/IUploadAndDownload'
        headers = {
            'x-huawei-uploadSrc': '1',
            'x-ClientOprType': '11',
            'Connection': 'keep-alive',
            'x-NetType': '6',
            'x-DeviceInfo': '6|127.0.0.1|1|10.0.1|Xiaomi|M2012K10C|********************************|02-00-00-00-00-00|android 11|1080X2272|zh||||032|',
            'x-huawei-channelSrc': '********',
            'x-MM-Source': '032',
            'x-SvcType': '1',
            'APP_NUMBER': self.account,
            'Authorization': self.authorization,
            'X-Tingyun-Id': 'p35OnrDoP8k;c=2;r=**********;u=43ee994e8c3a6057970124db00b2442c::8B3D3F05462B6E4C',
            'Host': 'ose.caiyun.feixin.10086.cn',
            'User-Agent': 'okhttp/3.11.0',
            'Content-Type': 'application/xml; charset=UTF-8',
            'Accept': '*/*'
        }
        payload = '''
                                <pcUploadFileRequest>
                                    <ownerMSISDN>{phone}</ownerMSISDN>
                                    <fileCount>1</fileCount>
                                    <totalSize>1</totalSize>
                                    <uploadContentList length="1">
                                        <uploadContentInfo>
                                            <comlexFlag>0</comlexFlag>
                                            <contentDesc><![CDATA[]]></contentDesc>
                                            <contentName><![CDATA[000000.txt]]></contentName>
                                            <contentSize>1</contentSize>
                                            <contentTAGList></contentTAGList>
                                            <digest>C4CA4238A0B923820DCC509A6F75849B</digest>
                                            <exif/>
                                            <fileEtag>0</fileEtag>
                                            <fileVersion>0</fileVersion>
                                            <updateContentID></updateContentID>
                                        </uploadContentInfo>
                                    </uploadContentList>
                                    <newCatalogName></newCatalogName>
                                    <parentCatalogID></parentCatalogID>
                                    <operation>0</operation>
                                    <path></path>
                                    <manualRename>2</manualRename>
                                    <autoCreatePath length="0"/>
                                    <tagID></tagID>
                                    <tagType></tagType>
                                </pcUploadFileRequest>
                            '''.format(phone = self.account)

        response = requests.post(url = url, headers = headers, data = payload)
        if response is None:
            return
        if response.status_code != 200:
            self.logger.error('❌ 上传文件失败')
        self.logger.info('✅ 上传文件成功')

    # 创建笔记任务
    def create_note(self, headers):
        note_id = self.get_note_id(32)  # 获取随机笔记id
        createtime = str(int(round(time.time() * 1000)))
        time.sleep(3)
        updatetime = str(int(round(time.time() * 1000)))
        note_url = 'http://mnote.caiyun.feixin.10086.cn/noteServer/api/createNote.do'
        payload = {
            "archived": 0,
            "attachmentdir": note_id,
            "attachmentdirid": "",
            "attachments": [],
            "audioInfo": {
                "audioDuration": 0,
                "audioSize": 0,
                "audioStatus": 0
            },
            "contentid": "",
            "contents": [{
                "contentid": 0,
                "data": "<font size=\"3\">000000</font>",
                "noteId": note_id,
                "sortOrder": 0,
                "type": "RICHTEXT"
            }],
            "cp": "",
            "createtime": createtime,
            "description": "android",
            "expands": {
                "noteType": 0
            },
            "latlng": "",
            "location": "",
            "noteid": note_id,
            "notestatus": 0,
            "remindtime": "",
            "remindtype": 1,
            "revision": "1",
            "sharecount": "0",
            "sharestatus": "0",
            "system": "mobile",
            "tags": [{
                "id": self.notebook_id,
                "orderIndex": "0",
                "text": "默认笔记本"
            }],
            "title": "00000",
            "topmost": "0",
            "updatetime": updatetime,
            "userphone": self.account,
            "version": "1.00",
            "visitTime": ""
        }
        create_note_data = requests.post(note_url, headers = headers, data = payload)
        if create_note_data.status_code == 200:
            self.logger.info('创建笔记成功')
        else:
            self.logger.error('创建失败')

    # 笔记id
    def get_note_id(self, length):
        characters = '19f3a063d67e4694ca63a4227ec9a94a19088404f9a28084e3e486b928039a299bf756ebc77aa4f6bfa250308ec6a8be8b63b5271a00350d136d117b8a72f39c5bd15cdfd350cba4271dc797f15412d9f269e666aea5039f5049d00739b320bb9e8585a008b52c1cbd86970cae9476446f3e41871de8d9f6112db94b05e5dc7ea0a942a9daf145ac8e487d3d5cba7cea145680efc64794d43dd15c5062b81e1cda7bf278b9bc4e1b8955846e6bc4b6a61c28f831f81b2270289e5a8a677c3141ddc9868129060c0c3b5ef507fbd46c004f6de346332ef7f05c0094215eae1217ee7c13c8dca6d174cfb49c716dd42903bb4b02d823b5f1ff93c3f88768251b56cc'
        note_id = ''.join(random.choice(characters) for _ in range(length))
        return note_id

    # 公众号签到
    @catch_errors
    def wxsign(self):
        self.sleep(min_delay=5, max_delay=10)
        url = f'{self.common_url}/market/playoffic/followSignInfo?isWx=true'
        return_data = self.session.get(url, headers = self.jwt_headers, cookies = self.cookies).json()
        if return_data['msg'] != 'success':
            self.logger.info(f"❌ 签到失败: {return_data['msg']}")
            return False
        if not return_data['result'].get('todaySignIn'):
            self.logger.error('❌ 签到失败,可能未绑定公众号')
            return False
        self.logger.info('✅ 签到成功')
        return True

    # 摇一摇
    def shake(self):
        url = f"{self.new_common_url}/market/shake-server/shake/shakeIt?flag=1"
        successful_shakes = 0  # 记录成功摇中的次数
        try:
            for _ in range(self.click_num):
                return_data = self.session.post(url).json()
                time.sleep(1)
                shake_prize_config = return_data["result"].get("shakePrizeconfig")
                if shake_prize_config:
                    self.logger.info(f"🎉 摇一摇获得: {shake_prize_config['name']}")
                    successful_shakes += 1
        except Exception as e:
            self.logger.error(f'❌ 错误信息: {e}')
        if successful_shakes == 0:
            self.logger.info(f'❌ 未摇中 x {self.click_num}')

    # 查询剩余抽奖次数
    @catch_errors
    def surplus_num(self):
        self.sleep()
        draw_info_url = f'{self.common_url}/market/playoffic/drawInfo'
        draw_url =  f"{self.common_url}/market/playoffic/draw"
        draw_info_data = self.session.get(draw_info_url).json()
        if draw_info_data.get('msg') == 'success':
            remain_num = draw_info_data['result'].get('surplusNumber', 0)
            self.logger.info(f'剩余抽奖次数 {remain_num}')
            if remain_num > 50 - self.draw:
                for _ in range(self.draw):
                    self.sleep()
                    draw_data = self.session.get(url = draw_url).json()
                    if draw_data.get("code") == 0:
                        prize_name = draw_data["result"].get("prizeName", "")
                        self.logger.info("✅ 抽奖成功，获得:" + prize_name)
                    else:
                        self.logger.info("❌ 抽奖失败")
            else:
                pass
        else:
            self.logger.error(draw_info_data.get('msg'))
            #self.log_info(draw_info_data.get('msg'))

    # 云朵大作战
    @catch_errors
    def cloud_game(self):
        game_info_url = f'{self.common_url}/market/signin/hecheng1T/info?op=info'
        bigin_url = f'{self.common_url}/market/signin/hecheng1T/beinvite'
        end_url = f'{self.common_url}/market/signin/hecheng1T/finish?flag=true'

        game_info_data = self.session.get(game_info_url).json()
        if game_info_data and game_info_data.get('code', -1) == 0:
            currnum = game_info_data.get('result', {}).get('info', {}).get('curr', 0)
            count = game_info_data.get('result', {}).get('history', {}).get('0', {}).get('count', '')
            rank = game_info_data.get('result', {}).get('history', {}).get('0', {}).get('rank', '')

            self.logger.info(f'今日剩余游戏次数: {currnum}\n本月排名: {rank}    合成次数: {count}')

            for _ in range(currnum):
                self.session.get(bigin_url).json()
                self.logger.info('开始游戏,等待10-15秒完成游戏')
                time.sleep(random.randint(10, 15))
                end_data = self.session.get(end_url).json()
                if end_data and end_data.get('code', -1) == 0:
                    self.logger.info('游戏成功')
        else:
            self.logger.error("❌ 获取游戏信息失败")

    # 领取云朵
    @catch_errors
    def receive(self):
        receive_url = f"{self.common_url}/market/signin/page/receive"
        prize_url = f"{self.common_url}/market/prizeApi/checkPrize/getUserPrizeLogPage?currPage=1&pageSize=15&_={self.timestamp}"
        receive_data = self.session.get(receive_url).json()
        self.sleep()
        prize_data = self.session.get(prize_url).json()
        result = prize_data.get('result').get('result')
        rewards = ''
        for value in result:
            prizeName = value.get('prizeName')
            flag = value.get('flag')
            if flag == 1:
                rewards += f'  {prizeName}\n'

        self.wait_receive_clouds = receive_data["result"].get("receive", 0)
        self.wait_receive_rewards = rewards.strip()
        #total_amount = receive_data["result"].get("total", "")
        #print(f'-当前云朵数量:{total_amount}云朵')

    # 领取自动备份云朵
    def backup_cloud(self):
        backup_url = f'{self.common_url}/market/backupgift/info'
        backup_data = self.session.get(backup_url).json()
        state = backup_data.get('result', {}).get('state', '')
        if state == -1:
            self.logger.info('本月未备份,暂无连续备份奖励')
        elif state == 0:
            self.logger.info('领取本月连续备份奖励')
            cur_url = f'{self.common_url}/market/backupgift/receive'
            cur_data = self.session.get(cur_url).json()
            self.logger.info(f'获得云朵数量:{cur_data.get("result").get("result")}')

        elif state == 1:
            self.logger.info('已领取本月连续备份奖励')
        self.sleep()
        expend_url = f'{self.common_url}/market/signin/page/taskExpansion'  # 每月膨胀云朵
        expend_data = self.session.get(expend_url).json()
        curMonthBackup = expend_data.get('result', {}).get('curMonthBackup', '')  # 本月备份
        preMonthBackup = expend_data.get('result', {}).get('preMonthBackup', '')  # 上月备份
        curMonthBackupTaskAccept = expend_data.get('result', {}).get('curMonthBackupTaskAccept', '')  # 本月是否领取
        nextMonthTaskRecordCount = expend_data.get('result', {}).get('nextMonthTaskRecordCount', '')  # 下月备份云朵
        acceptDate = expend_data.get('result', {}).get('acceptDate', '')  # 月份
        if curMonthBackup:
            self.logger.info(f'✅ 本月已备份，下月可领取膨胀云朵: {nextMonthTaskRecordCount}')
        else:
            self.logger.info('本月还未备份，下月暂无膨胀云朵')
        if preMonthBackup:
            if curMonthBackupTaskAccept:
                self.logger.info('✅ 上月已备份，膨胀云朵已领取')
            else:
                # 领取
                receive_url = f'{self.common_url}/market/signin/page/receiveTaskExpansion?acceptDate={acceptDate}'
                receive_data = self.session.get(receive_url).json()
                if receive_data.get("code") != 0:
                    self.logger.info(f'❌ 领取失败:{receive_data.get("msg")}')
                else:
                    cloudCount = receive_data.get('result', {}).get('cloudCount', '')
                    self.logger.info(f'膨胀云朵领取成功: {cloudCount}朵')
        else:
            self.logger.info('❌ 上月未备份，本月无膨胀云朵领取')

    # 领取通知开启云朵
    @catch_errors
    def open_send(self):
        send_url = f'{self.new_common_url}/market/msgPushOn/task/status'
        send_data = self.session.get(send_url, headers = self.jwt_headers).json()

        pushOn = send_data.get('result', {}).get('pushOn', '')  # 0未开启，1开启，2未领取，3已领取
        firstTaskStatus = send_data.get('result', {}).get('firstTaskStatus', '')
        secondTaskStatus = send_data.get('result', {}).get('secondTaskStatus', '')
        onDuaration = send_data.get('result', {}).get('onDuaration', '')  # 开启时间

        if pushOn == 1:
            reward_url = f'{self.new_common_url}/market/msgPushOn/task/obtain'
            payload = {
                "type": 2
            }
            if firstTaskStatus == 3:
                self.logger.info('ℹ️  通知首次开启奖励已领取')
            else:
                self.logger.info('领取通知首次开启奖励')
                reward1_data = self.session.post(reward_url, data=json.dumps(payload)).json()
                self.logger.info(reward1_data.get('result', {}).get('description', ''))
            if secondTaskStatus == 2:
                self.logger.info('领取通知满31天奖励')
                reward2_data = {}
                try:
                    reward2_resp = self.session.post(reward_url, data = {"type": 2})
                    reward2_data = reward2_resp.json()
                except Exception:
                    self.logger.info(f'❌ 领取通知满31天奖励失败，响应 Text: {reward2_resp.text}，响应 JSON: {reward2_data}')
                    return
                self.logger.info(reward2_data.get('result', {}).get('description', ''))

            self.logger.info(f'ℹ️  通知已开启天数: {onDuaration}, 满31天可领取奖励')
        else:
            self.logger.info('❌ 通知权限未开启')

        """139 邮箱领取短信通知开启奖励"""
        mail_send_url = f'{self.common_url}:7071/ycloud/openemailsms-service/openEmailsms/getTaskInfo'
        mail_send_data = self.session.get(mail_send_url, headers = self.jwt_headers).json()
        taskOne = mail_send_data.get('result', {}).get('taskOneInfo', '')
        if taskOne.get("cloudStatus") == 2:
            self.logger.info(f"ℹ️  139邮箱首次开启短信通知奖励已领取")

        taskTwo = mail_send_data.get('result', {}).get('taskTwoInfo', '')
        if taskTwo.get("cloudStatus") == 1:
            reward_sum = taskTwo.get("sum")
            mail_receive_url = f'{self.common_url}:7071/ycloud/openemailsms-service/openEmailsms/reward'
            mail_receive_data = self.session.get(mail_receive_url, headers = self.jwt_headers).json()
            if mail_receive_data.get('code', None) == 0:
                self.logger.info(f"✅ 领取 139邮箱开启短信通知奖励成功, 获得云朵数量: {reward_sum}")
            else:
                self.logger.info(f"⚠️ 领取 139邮箱开启短信通知奖励失败, 原因: {mail_receive_data.get('msg')}")
        else:
            self.logger.info(f"ℹ️  139邮箱已开启短信通知天数: {taskTwo.get('progress')} 天, 满31天可领取奖励")

if __name__ == "__main__":
    app_name = "中国移动云盘"
    app_env_name = "ZGYDYP_CREDENTIALS"
    local_script_name = os.path.basename(__file__)
    local_version = '2025.05.05'
    run = Run(app_name=app_name,
              app_env_name=app_env_name,
              script_version=local_version)
    run.main()
